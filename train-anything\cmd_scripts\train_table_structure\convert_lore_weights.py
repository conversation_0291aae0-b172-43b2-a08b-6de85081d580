#!/usr/bin/env python3
"""
LORE-TSR 权重转换脚本

迭代8步骤8.3：权重转换脚本和配置集成
独立的命令行工具，支持LORE-TSR权重转换为train-anything格式
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from modules.utils.lore_tsr.weight_converter import LoreTsrWeightConverter
from modules.utils.lore_tsr.weight_validator import LoreTsrWeightValidator
from modules.utils.lore_tsr.weight_utils import validate_weight_file, get_weight_file_info

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_argparser() -> argparse.ArgumentParser:
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='LORE-TSR权重转换工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本转换
  python convert_lore_weights.py \\
    --model-path /path/to/model_best.pth \\
    --processor-path /path/to/processor_best.pth \\
    --output-path /path/to/converted/pytorch_model.bin

  # 带验证的转换
  python convert_lore_weights.py \\
    --model-path /path/to/model_best.pth \\
    --processor-path /path/to/processor_best.pth \\
    --output-path /path/to/converted/pytorch_model.bin \\
    --validate

  # 批量转换
  python convert_lore_weights.py \\
    --input-dir /path/to/lore_checkpoints \\
    --output-dir /path/to/converted_checkpoints \\
    --batch
        """
    )
    
    # 单个文件转换参数
    parser.add_argument('--model-path', type=str,
                       help='LORE-TSR模型权重文件路径 (model_best.pth)')
    parser.add_argument('--processor-path', type=str,
                       help='LORE-TSR处理器权重文件路径 (processor_best.pth)')
    parser.add_argument('--output-path', type=str,
                       help='转换后权重保存路径 (pytorch_model.bin)')
    
    # 批量转换参数
    parser.add_argument('--input-dir', type=str,
                       help='LORE-TSR权重文件目录（批量转换）')
    parser.add_argument('--output-dir', type=str,
                       help='转换后权重保存目录（批量转换）')
    parser.add_argument('--batch', action='store_true',
                       help='启用批量转换模式')
    
    # 验证和配置参数
    parser.add_argument('--validate', action='store_true',
                       help='验证转换结果的正确性')
    parser.add_argument('--strict', action='store_true',
                       help='启用严格模式（所有权重必须匹配）')
    parser.add_argument('--tolerance', type=float, default=1e-6,
                       help='数值容差 (默认: 1e-6)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细日志')
    
    return parser


def convert_single_checkpoint(model_path: str, processor_path: str, 
                            output_path: str, validate: bool = False,
                            strict: bool = False, tolerance: float = 1e-6) -> bool:
    """
    转换单个检查点
    
    Args:
        model_path (str): LORE-TSR模型权重路径
        processor_path (str): LORE-TSR处理器权重路径
        output_path (str): 输出路径
        validate (bool): 是否验证转换结果
        strict (bool): 是否启用严格模式
        tolerance (float): 数值容差
        
    Returns:
        bool: 转换是否成功
    """
    try:
        logger.info(f"开始转换单个检查点")
        logger.info(f"  模型权重: {model_path}")
        logger.info(f"  处理器权重: {processor_path}")
        logger.info(f"  输出路径: {output_path}")
        
        # 验证输入文件
        if not validate_weight_file(model_path):
            logger.error(f"模型权重文件无效: {model_path}")
            return False
            
        if not validate_weight_file(processor_path):
            logger.error(f"处理器权重文件无效: {processor_path}")
            return False
        
        # 创建转换器
        config = {
            'strict_mode': strict,
            'validate_conversion': validate,
            'tolerance': tolerance
        }
        converter = LoreTsrWeightConverter(config)
        
        # 执行转换
        success = converter.convert_lore_weights(model_path, processor_path, output_path)
        
        if not success:
            logger.error("权重转换失败")
            return False
        
        # 验证转换结果
        if validate:
            logger.info("开始验证转换结果")
            validation_success = validate_conversion(
                model_path, processor_path, output_path, tolerance
            )
            if not validation_success:
                logger.warning("转换验证失败，但转换文件已生成")
        
        logger.info("单个检查点转换完成")
        return True
        
    except Exception as e:
        logger.error(f"转换单个检查点失败: {e}")
        return False


def convert_batch_checkpoints(input_dir: str, output_dir: str, 
                            validate: bool = False, strict: bool = False,
                            tolerance: float = 1e-6) -> Dict[str, bool]:
    """
    批量转换检查点
    
    Args:
        input_dir (str): 输入目录
        output_dir (str): 输出目录
        validate (bool): 是否验证转换结果
        strict (bool): 是否启用严格模式
        tolerance (float): 数值容差
        
    Returns:
        dict: 转换结果字典 {checkpoint_name: success}
    """
    try:
        logger.info(f"开始批量转换检查点")
        logger.info(f"  输入目录: {input_dir}")
        logger.info(f"  输出目录: {output_dir}")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 查找LORE-TSR权重文件对
        checkpoint_pairs = find_checkpoint_pairs(input_dir)
        
        if not checkpoint_pairs:
            logger.warning(f"在目录 {input_dir} 中未找到有效的LORE-TSR权重文件对")
            return {}
        
        logger.info(f"找到 {len(checkpoint_pairs)} 个权重文件对")
        
        results = {}
        
        for checkpoint_name, (model_path, processor_path) in checkpoint_pairs.items():
            logger.info(f"转换检查点: {checkpoint_name}")
            
            # 生成输出路径
            output_path = os.path.join(output_dir, f"{checkpoint_name}_pytorch_model.bin")
            
            # 转换单个检查点
            success = convert_single_checkpoint(
                model_path, processor_path, output_path, validate, strict, tolerance
            )
            
            results[checkpoint_name] = success
            
            if success:
                logger.info(f"✅ {checkpoint_name} 转换成功")
            else:
                logger.error(f"❌ {checkpoint_name} 转换失败")
        
        # 输出批量转换结果
        success_count = sum(results.values())
        total_count = len(results)
        logger.info(f"批量转换完成: {success_count}/{total_count} 成功")
        
        return results
        
    except Exception as e:
        logger.error(f"批量转换检查点失败: {e}")
        return {}


def find_checkpoint_pairs(input_dir: str) -> Dict[str, Tuple[str, str]]:
    """
    在目录中查找LORE-TSR权重文件对
    
    Args:
        input_dir (str): 输入目录
        
    Returns:
        dict: {checkpoint_name: (model_path, processor_path)}
    """
    pairs = {}
    
    # 查找所有.pth文件
    pth_files = list(Path(input_dir).glob('*.pth'))
    
    # 按文件名分组
    model_files = [f for f in pth_files if 'model' in f.name.lower()]
    processor_files = [f for f in pth_files if 'processor' in f.name.lower()]
    
    # 匹配文件对
    for model_file in model_files:
        model_name = model_file.stem
        # 尝试找到对应的processor文件
        for processor_file in processor_files:
            processor_name = processor_file.stem
            
            # 简单的名称匹配逻辑
            if ('best' in model_name and 'best' in processor_name) or \
               ('last' in model_name and 'last' in processor_name):
                checkpoint_name = 'best' if 'best' in model_name else 'last'
                pairs[checkpoint_name] = (str(model_file), str(processor_file))
                break
    
    return pairs


def validate_conversion(original_model_path: str, original_processor_path: str,
                       converted_path: str, tolerance: float = 1e-6) -> bool:
    """
    验证转换结果
    
    Args:
        original_model_path (str): 原始模型权重路径
        original_processor_path (str): 原始处理器权重路径
        converted_path (str): 转换后权重路径
        tolerance (float): 数值容差
        
    Returns:
        bool: 验证是否通过
    """
    try:
        logger.info("开始验证转换结果")
        
        # 创建验证器
        config = {
            'tolerance': tolerance,
            'mode': 'basic',
            'save_report': True
        }
        validator = LoreTsrWeightValidator(config)
        
        # 加载原始权重
        import torch
        original_model = torch.load(original_model_path, map_location='cpu')
        original_processor = torch.load(original_processor_path, map_location='cpu')
        
        # 提取状态字典
        if isinstance(original_model, dict) and 'state_dict' in original_model:
            original_model = original_model['state_dict']
        if isinstance(original_processor, dict) and 'state_dict' in original_processor:
            original_processor = original_processor['state_dict']
        
        # 合并原始权重
        original_weights = {}
        original_weights.update(original_model)
        original_weights.update(original_processor)
        
        # 加载转换后权重
        converted_weights = torch.load(converted_path, map_location='cpu')
        
        # 执行验证
        result = validator.validate_converted_weights(original_weights, converted_weights)
        
        if result.success:
            logger.info("✅ 转换验证通过")
        else:
            logger.warning("⚠️ 转换验证未完全通过")
            if result.error_message:
                logger.warning(f"验证错误: {result.error_message}")
        
        # 生成验证报告
        report = validator.generate_validation_report(result)
        report_path = converted_path.replace('.bin', '_validation_report.txt')
        validator.save_validation_report(report, report_path)
        
        return result.success
        
    except Exception as e:
        logger.error(f"验证转换结果失败: {e}")
        return False


def main():
    """脚本主入口，处理命令行参数并执行转换"""
    parser = setup_argparser()
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # 检查参数
        if args.batch:
            # 批量转换模式
            if not args.input_dir or not args.output_dir:
                logger.error("批量转换模式需要指定 --input-dir 和 --output-dir")
                return 1
            
            results = convert_batch_checkpoints(
                args.input_dir, args.output_dir, 
                args.validate, args.strict, args.tolerance
            )
            
            success_count = sum(results.values())
            total_count = len(results)
            
            if success_count == total_count and total_count > 0:
                logger.info("🎉 所有检查点转换成功")
                return 0
            else:
                logger.error(f"❌ 部分检查点转换失败: {success_count}/{total_count}")
                return 1
                
        else:
            # 单个文件转换模式
            if not args.model_path or not args.processor_path or not args.output_path:
                logger.error("单个文件转换模式需要指定 --model-path, --processor-path 和 --output-path")
                return 1
            
            success = convert_single_checkpoint(
                args.model_path, args.processor_path, args.output_path,
                args.validate, args.strict, args.tolerance
            )
            
            if success:
                logger.info("🎉 权重转换成功")
                return 0
            else:
                logger.error("❌ 权重转换失败")
                return 1
                
    except KeyboardInterrupt:
        logger.info("用户中断转换")
        return 1
    except Exception as e:
        logger.error(f"转换过程中出现未预期的错误: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
