# ------------------------------------------------------------------------------
# Copyright (c) Microsoft
# Licensed under the MIT License.
# Written by <PERSON> (Bin<PERSON><EMAIL>)
# Modified by <PERSON><PERSON><PERSON>
# ------------------------------------------------------------------------------

"""
LORE-TSR 图像处理工具模块

完整迁移自 LORE-TSR/src/lib/utils/image.py
迭代5步骤5.1：基础工具函数迁移（最小可验证单元）

本模块包含LORE-TSR核心算法的图像处理工具函数：
- 仿射变换相关函数
- 高斯热力图生成函数  
- 颜色增强函数
- 其他辅助工具函数

严格遵循"复制保留核心算法"策略，逐行复制原始实现，严禁任何优化或重构。
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import numpy as np
import cv2
import random


def flip(img):
    """图像水平翻转（直接迁移自LORE-TSR）"""
    return img[:, :, ::-1].copy()


def transform_preds(coords, center, scale, output_size, rot=0):
    """
    变换预测坐标（直接迁移自LORE-TSR）
    
    Args:
        coords: 输入坐标
        center: 变换中心点
        scale: 缩放因子
        output_size: 输出尺寸
        rot: 旋转角度（默认0）
        
    Returns:
        变换后的坐标
    """
    target_coords = np.zeros(coords.shape)
    trans = get_affine_transform(center, scale, rot, output_size, inv=1)
    for p in range(coords.shape[0]):
        target_coords[p, 0:2] = affine_transform(coords[p, 0:2], trans)
    return target_coords


def transform_preds_upper_left(coords, center, scale, output_size, rot=0):
    """
    左上角模式变换预测坐标（直接迁移自LORE-TSR）
    
    Args:
        coords: 输入坐标
        center: 变换中心点
        scale: 缩放因子
        output_size: 输出尺寸
        rot: 旋转角度（默认0）
        
    Returns:
        变换后的坐标
    """
    target_coords = np.zeros(coords.shape)
    trans = get_affine_transform_upper_left(center, scale, rot, output_size, inv=1)
    for p in range(coords.shape[0]):
        target_coords[p, 0:2] = affine_transform(coords[p, 0:2], trans)
    return target_coords


def get_affine_transform_upper_left(center,
                         scale,
                         rot,
                         output_size,
                         shift=np.array([0, 0], dtype=np.float32),
                         inv=0):
    """
    计算左上角模式的仿射变换矩阵（直接迁移自LORE-TSR/src/lib/utils/image.py第33行）
    
    Args:
        center: 变换中心点 [x, y]
        scale: 缩放因子或尺寸
        rot: 旋转角度（度）
        output_size: 输出尺寸 [width, height]
        shift: 平移偏移（默认[0,0]）
        inv: 是否逆变换（默认0）
        
    Returns:
        np.ndarray: 2x3仿射变换矩阵
    """
    if not isinstance(scale, np.ndarray) and not isinstance(scale, list):
        scale = np.array([scale, scale], dtype=np.float32)
   
    src = np.zeros((3, 2), dtype=np.float32)
    dst = np.zeros((3, 2), dtype=np.float32)
    src[0, :] = center 
    dst[0, :] = [0,0]
    if center[0] < center[1]:
        src[1, :] = [scale[0], center[1]] 
        dst[1, :] = [output_size[0], 0] 
    else:
        src[1, :] = [center[0], scale[0]] 
        dst[1, :] = [0, output_size[0]]
    src[2:, :] = get_3rd_point(src[0, :], src[1, :])
    dst[2:, :] = get_3rd_point(dst[0, :], dst[1, :])

    if inv:
        trans = cv2.getAffineTransform(np.float32(dst), np.float32(src))
    else:
        trans = cv2.getAffineTransform(np.float32(src), np.float32(dst))

    return trans


def get_affine_transform(center,
                         scale,
                         rot,
                         output_size,
                         shift=np.array([0, 0], dtype=np.float32),
                         inv=0):
    """
    计算仿射变换矩阵（直接迁移自LORE-TSR/src/lib/utils/image.py第62行）
    
    Args:
        center: 变换中心点 [x, y]
        scale: 缩放因子或尺寸
        rot: 旋转角度（度）
        output_size: 输出尺寸 [width, height]
        shift: 平移偏移（默认[0,0]）
        inv: 是否逆变换（默认0）
        
    Returns:
        np.ndarray: 2x3仿射变换矩阵
    """
    if not isinstance(scale, np.ndarray) and not isinstance(scale, list):
        scale = np.array([scale, scale], dtype=np.float32)

    scale_tmp = scale
    src_w = scale_tmp[0]
    dst_w = output_size[0]
    dst_h = output_size[1]

    rot_rad = np.pi * rot / 180
    src_dir = get_dir([0, src_w * -0.5], rot_rad)
    dst_dir = np.array([0, dst_w * -0.5], np.float32)

    src = np.zeros((3, 2), dtype=np.float32)
    dst = np.zeros((3, 2), dtype=np.float32)
    src[0, :] = center + scale_tmp * shift #[0,0]#
    src[1, :] = center + src_dir + scale_tmp * shift #scale#
    dst[0, :] = [dst_w * 0.5, dst_h * 0.5] #[0,0]#

    #if scale_tmp < dst_w: #added
       #dst[1, :] = scale #added
    #else:
    dst[1, :] = np.array([dst_w * 0.5, dst_h * 0.5], np.float32) + dst_dir #output_size #

    src[2:, :] = get_3rd_point(src[0, :], src[1, :])
    dst[2:, :] = get_3rd_point(dst[0, :], dst[1, :])

    if inv:
        trans = cv2.getAffineTransform(np.float32(dst), np.float32(src))
    else:
        trans = cv2.getAffineTransform(np.float32(src), np.float32(dst))

    return trans


def affine_transform(pt, t):
    """
    对点进行仿射变换（直接迁移自LORE-TSR/src/lib/utils/image.py）
    
    Args:
        pt: 输入点坐标 [x, y]
        t: 仿射变换矩阵
        
    Returns:
        np.ndarray: 变换后的点坐标
    """
    new_pt = np.array([pt[0], pt[1], 1.], dtype=np.float32).T
    new_pt = np.dot(t, new_pt)
    return new_pt[:2]


def get_3rd_point(a, b):
    """
    计算第三个点（直接迁移自LORE-TSR）
    
    Args:
        a: 第一个点
        b: 第二个点
        
    Returns:
        第三个点坐标
    """
    direct = a - b
    return b + np.array([-direct[1], direct[0]], dtype=np.float32)


def get_dir(src_point, rot_rad):
    """
    计算旋转方向（直接迁移自LORE-TSR）
    
    Args:
        src_point: 源点
        rot_rad: 旋转弧度
        
    Returns:
        旋转后的方向
    """
    sn, cs = np.sin(rot_rad), np.cos(rot_rad)

    src_result = [0, 0]
    src_result[0] = src_point[0] * cs - src_point[1] * sn
    src_result[1] = src_point[0] * sn + src_point[1] * cs

    return src_result


def crop(img, center, scale, output_size, rot=0):
    """
    图像裁剪（直接迁移自LORE-TSR）
    
    Args:
        img: 输入图像
        center: 中心点
        scale: 缩放因子
        output_size: 输出尺寸
        rot: 旋转角度（默认0）
        
    Returns:
        裁剪后的图像
    """
    trans = get_affine_transform(center, scale, rot, output_size)

    dst_img = cv2.warpAffine(img,
                             trans,
                             (int(output_size[0]), int(output_size[1])),
                             flags=cv2.INTER_LINEAR)

    return dst_img


def gaussian_radius(det_size, min_overlap=0.7):
    """
    计算高斯半径（直接迁移自LORE-TSR原项目）

    Args:
        det_size: 检测框尺寸 (height, width)
        min_overlap: 最小重叠率（默认0.7）

    Returns:
        int: 高斯半径
    """
    height, width = det_size

    a1  = 1
    b1  = (height + width)
    c1  = width * height * (1 - min_overlap) / (1 + min_overlap)
    sq1 = np.sqrt(b1 ** 2 - 4 * a1 * c1)
    r1  = (b1 + sq1) / 2

    a2  = 4
    b2  = 2 * (height + width)
    c2  = (1 - min_overlap) * width * height
    sq2 = np.sqrt(b2 ** 2 - 4 * a2 * c2)
    r2  = (b2 + sq2) / 2

    a3  = 4 * min_overlap
    b3  = -2 * min_overlap * (height + width)
    c3  = (min_overlap - 1) * width * height
    sq3 = np.sqrt(b3 ** 2 - 4 * a3 * c3)
    r3  = (b3 + sq3) / 2
    return min(r1, r2, r3)


def gaussian2D(shape, sigma=1):
    """
    生成2D高斯分布（直接迁移自LORE-TSR）

    Args:
        shape: 高斯分布形状
        sigma: 标准差（默认1）

    Returns:
        2D高斯分布数组
    """
    m, n = [(ss - 1.) / 2. for ss in shape]
    y, x = np.ogrid[-m:m+1,-n:n+1]

    h = np.exp(-(x * x + y * y) / (2 * sigma * sigma))
    h[h < np.finfo(h.dtype).eps * h.max()] = 0
    return h


def draw_umich_gaussian_wh(heatmap, center, r_w, r_h, k=1):
    """
    绘制宽高不同的高斯分布（直接迁移自LORE-TSR）

    Args:
        heatmap: 目标热力图
        center: 中心点坐标
        r_w: 宽度半径
        r_h: 高度半径
        k: 高斯强度（默认1）

    Returns:
        更新后的热力图
    """
    diameter_x = 2 * r_w + 1
    diameter_y = 2 * r_h + 1
    gaussian = gaussian2D((diameter_y, diameter_x), sigma=min(r_w,r_h) / 6)

    x, y = int(center[0]), int(center[1])

    height, width = heatmap.shape[0:2]

    left, right = min(x, r_w), min(width - x, r_w + 1)
    top, bottom = min(y, r_h), min(height - y, r_h + 1)

    masked_heatmap  = heatmap[y - top:y + bottom, x - left:x + right]
    masked_gaussian = gaussian[r_h - top:r_h + bottom, r_w - left:r_w + right]
    if min(masked_gaussian.shape) > 0 and min(masked_heatmap.shape) > 0: # TODO debug
        np.maximum(masked_heatmap, masked_gaussian * k, out=masked_heatmap)
    return heatmap


def draw_umich_gaussian(heatmap, center, radius, k=1):
    """
    在热力图上绘制高斯分布（直接迁移自LORE-TSR原项目）

    Args:
        heatmap: 目标热力图张量 [H, W]
        center: 中心点坐标 [x, y]
        radius: 高斯半径
        k: 高斯强度（默认1）

    Returns:
        np.ndarray: 更新后的热力图
    """
    diameter = 2 * radius + 1
    gaussian = gaussian2D((diameter, diameter), sigma=diameter / 6)

    x, y = int(center[0]), int(center[1])

    height, width = heatmap.shape[0:2]

    left, right = min(x, radius), min(width - x, radius + 1)
    top, bottom = min(y, radius), min(height - y, radius + 1)

    masked_heatmap  = heatmap[y - top:y + bottom, x - left:x + right]
    masked_gaussian = gaussian[radius - top:radius + bottom, radius - left:radius + right]
    if min(masked_gaussian.shape) > 0 and min(masked_heatmap.shape) > 0: # TODO debug
        np.maximum(masked_heatmap, masked_gaussian * k, out=masked_heatmap)
    return heatmap


def draw_dense_reg(regmap, heatmap, center, value, radius, is_offset=False):
    """
    绘制密集回归图（直接迁移自LORE-TSR）

    Args:
        regmap: 回归图
        heatmap: 热力图
        center: 中心点
        value: 回归值
        radius: 半径
        is_offset: 是否为偏移（默认False）

    Returns:
        更新后的回归图
    """
    diameter = 2 * radius + 1
    gaussian = gaussian2D((diameter, diameter), sigma=diameter / 6)
    value = np.array(value, dtype=np.float32).reshape(-1, 1, 1)
    dim = value.shape[0]
    reg = np.ones((dim, diameter*2+1, diameter*2+1), dtype=np.float32) * value
    if is_offset and dim == 2:
        delta = np.arange(diameter*2+1) - radius
        reg[0] = reg[0] - delta.reshape(1, -1)
        reg[1] = reg[1] - delta.reshape(-1, 1)

    x, y = int(center[0]), int(center[1])

    height, width = heatmap.shape[0:2]

    left, right = min(x, radius), min(width - x, radius + 1)
    top, bottom = min(y, radius), min(height - y, radius + 1)

    masked_heatmap = heatmap[y - top:y + bottom, x - left:x + right]
    masked_regmap = regmap[:, y - top:y + bottom, x - left:x + right]
    masked_gaussian = gaussian[radius - top:radius + bottom,
                             radius - left:radius + right]
    masked_reg = reg[:, radius - top:radius + bottom,
                      radius - left:radius + right]
    if min(masked_gaussian.shape) > 0 and min(masked_heatmap.shape) > 0: # TODO debug
        idx = (masked_gaussian >= masked_heatmap).reshape(
          1, masked_gaussian.shape[0], masked_gaussian.shape[1])
        masked_regmap = (1-idx) * masked_regmap + idx * masked_reg
    regmap[:, y - top:y + bottom, x - left:x + right] = masked_regmap
    return regmap


def draw_msra_gaussian(heatmap, center, sigma):
    """
    绘制MSRA风格高斯分布（直接迁移自LORE-TSR）

    Args:
        heatmap: 热力图
        center: 中心点
        sigma: 标准差

    Returns:
        更新后的热力图
    """
    tmp_size = sigma * 3
    mu_x = int(center[0] + 0.5)
    mu_y = int(center[1] + 0.5)
    w, h = heatmap.shape[0], heatmap.shape[1]
    ul = [int(mu_x - tmp_size), int(mu_y - tmp_size)]
    br = [int(mu_x + tmp_size + 1), int(mu_y + tmp_size + 1)]
    if ul[0] >= h or ul[1] >= w or br[0] < 0 or br[1] < 0:
        return heatmap
    size = 2 * tmp_size + 1
    x = np.arange(0, size, 1, np.float32)
    y = x[:, np.newaxis]
    x0 = y0 = size // 2
    g = np.exp(- ((x - x0) ** 2 + (y - y0) ** 2) / (2 * sigma ** 2))
    g_x = max(0, -ul[0]), min(br[0], h) - ul[0]
    g_y = max(0, -ul[1]), min(br[1], w) - ul[1]
    img_x = max(0, ul[0]), min(br[0], h)
    img_y = max(0, ul[1]), min(br[1], w)
    heatmap[img_y[0]:img_y[1], img_x[0]:img_x[1]] = np.maximum(
        heatmap[img_y[0]:img_y[1], img_x[0]:img_x[1]],
        g[g_y[0]:g_y[1], g_x[0]:g_x[1]])
    return heatmap


def grayscale(image):
    """
    转换为灰度图（直接迁移自LORE-TSR）

    Args:
        image: 输入图像

    Returns:
        灰度图像
    """
    return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)


def lighting_(data_rng, image, alphastd, eigval, eigvec):
    """
    光照增强（直接迁移自LORE-TSR）

    Args:
        data_rng: 随机数生成器
        image: 输入图像
        alphastd: alpha标准差
        eigval: 特征值
        eigvec: 特征向量
    """
    alpha = data_rng.normal(scale=alphastd, size=(3, ))
    image += np.dot(eigvec, eigval * alpha)


def blend_(alpha, image1, image2):
    """
    图像混合（直接迁移自LORE-TSR）

    Args:
        alpha: 混合系数
        image1: 第一张图像
        image2: 第二张图像
    """
    image1 *= alpha
    image2 *= (1 - alpha)
    image1 += image2


def saturation_(data_rng, image, gs, gs_mean, var):
    """
    饱和度调整（直接迁移自LORE-TSR）

    Args:
        data_rng: 随机数生成器
        image: 输入图像
        gs: 灰度图
        gs_mean: 灰度均值
        var: 变化范围
    """
    alpha = 1. + data_rng.uniform(low=-var, high=var)
    blend_(alpha, image, gs[:, :, None])


def brightness_(data_rng, image, gs, gs_mean, var):
    """
    亮度调整（直接迁移自LORE-TSR）

    Args:
        data_rng: 随机数生成器
        image: 输入图像
        gs: 灰度图
        gs_mean: 灰度均值
        var: 变化范围
    """
    alpha = 1. + data_rng.uniform(low=-var, high=var)
    image *= alpha


def contrast_(data_rng, image, gs, gs_mean, var):
    """
    对比度调整（直接迁移自LORE-TSR）

    Args:
        data_rng: 随机数生成器
        image: 输入图像
        gs: 灰度图
        gs_mean: 灰度均值
        var: 变化范围
    """
    alpha = 1. + data_rng.uniform(low=-var, high=var)
    blend_(alpha, image, gs_mean)


def color_aug(data_rng, image, eig_val, eig_vec):
    """
    颜色增强函数（直接迁移自LORE-TSR）

    Args:
        data_rng: 随机数生成器
        image: 输入图像
        eig_val: 特征值
        eig_vec: 特征向量
    """
    functions = [brightness_, contrast_, saturation_]
    random.shuffle(functions)

    gs = grayscale(image)
    gs_mean = gs.mean()
    for f in functions:
        f(data_rng, image, gs, gs_mean, 0.4)
    lighting_(data_rng, image, 0.1, eig_val, eig_vec)
