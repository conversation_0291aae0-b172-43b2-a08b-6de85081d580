---
trigger: manual
---

**角色:** 你是一名资深的AI架构师，精通PyTorch和HuggingFace生态，擅长将复杂的模型迁移和重构任务，规划为清晰、可执行、小步快跑的迭代开发计划。

**目标:** 为深度学习项目 `LORE-TSR` 迁移至现代化训练框架 `train-anything` 的任务，制定一份详细的需求规划和迭代计划。这份计划将是后续开发工作的唯一依据，必须细致、严谨，确保每个迭代步骤都是一个可独立开发和验证的模块。


---

### **背景信息**

#### **1. 整体迁移目标**

将表格结构识别项目 `LORE-TSR` 从其独立的、基于自定义脚本的架构，完整迁移到 `train-anything` 框架中。迁移的核心是适配 `train-anything` 的现代化训练流程（基于 `accelerate`）、配置管理（基于 `OmegaConf`）和数据加载机制，同时保持 `LORE-TSR` 核心模型与损失函数的初始定义和交互逻辑不变，以确保算法效果的可复现性。

#### **2. 源项目: `LORE-TSR` 架构分析**

- **入口**: `src/main.py`，一个庞大的主函数，负责所有初始化和流程控制。
- **配置**: 通过 `src/lib/opts.py` 使用 `argparse` 解析命令行参数，配置项硬编码在代码中。
- **训练脚本**：`src/scripts/train/train_wireless_arcres.sh`。
- **推理脚本**：`src/scripts/infer/demo_wireless_mine.sh`。
- **训练逻辑**: 自定义的 `BaseTrainer` 类 (`src/lib/trains/base_trainer.py`)，使用 `DataParallel` 实现多GPU训练。
- **模型结构**: 分离式设计，包含两个核心 `nn.Module`：
    1.  **`model`**: 负责视觉特征提取的检测器 (如 DLA, ResNet-DCN)，通过 `create_model` 工厂创建。
    2.  **`Processor`**: 负责逻辑结构恢复的Transformer模块 (`src/lib/models/classifier.py`)。
- **损失函数**: `CtdetLoss` (`src/lib/trains/ctdet.py`)，内部包含了热图、回归、结构等多个子损失的复杂计算逻辑。
- **数据集**: 通过 `get_dataset` 工厂 (`src/lib/datasets/dataset_factory.py`) 动态创建数据集类，数据格式与 `train-anything` 不兼容。
- **关键依赖**: 存在需要手动编译的外部依赖：`DCNv2` 和 `NMS` (Cython)。

#### **3. 目标框架: `train-anything` (参考 `cycle-centernet-ms` 实现)**

- **入口**: 独立的训练脚本，如 `training_loops/table_structure_recognition/train_cycle_centernet_ms.py`。
- **配置**: 使用 `OmegaConf` 解析层级化的 `YAML` 配置文件，如 `configs/table_structure_recognition/cycle_centernet/cycle_centernet_ms_config.yaml`。
- **训练逻辑**: 基于 HuggingFace `accelerate` 库，轻松支持分布式训练和混合精度。
- **模型结构**: 单一、集成的模型类 (如 `CycleCenterNetModel`)，通过模型工厂函数创建。
- **损失函数**: 单一、独立的损失类 (如 `CycleCenterNetLoss`)，通过损失工厂函数创建。
- **数据集**: 静态的 `TableDataset` 类 (`my_datasets/table_structure_recognition/table_dataset.py`)，支持标准化的目录结构和JSON标注格式，具备数据质量过滤功能。

---

请遵循以下要求：
- 不得讨论实现方案或编写代码，请聚焦于需求本身
- 按照优先级将需求拆分为多个迭代版本
- 每个迭代版本不必实现需求中所有功能，但是要求能够独立使用，并且每个迭代易于实现。
- 第一个迭代为MVP版

最终产出的文档将用于cursor(一款基于VSCode的LLM AI编程IDE)开发软件过程中，而非给人阅读，因此文档要严谨、细致，没有空话套话。

请将PRD整合进需求规划中，这份文档将是后续开发的唯一依据，它必须包含所有功能的优先级和详细定义，且不得出现引用PRD文档的字样。后续cursor开发只能看到这份文档，且迭代步骤必须为小颗粒、可独立开发和验证的模块，而非面向用户的版本。
