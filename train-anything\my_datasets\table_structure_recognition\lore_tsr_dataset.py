#!/usr/bin/env python3
"""
LORE-TSR 数据集类实现

迭代5步骤5.2：数据集基础框架
迭代5步骤5.3：核心数据处理pipeline
继承train-anything框架的TableDataset基类，支持WTW分布式标注格式
实现完整的LORE-TSR数据处理流程

本模块遵循"复制保留核心算法"策略：
- 逐行迁移ctdet.py第159-380行的核心逻辑
- 保持原有的数值计算精度和处理流程
- 正确集成步骤5.1的工具函数
- 严禁任何形式的优化或重构
"""

import os
import torch
import numpy as np
import cv2
import random
import math
from typing import Dict, Any, List, Tuple, Optional
import sys
from pathlib import Path
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from my_datasets.table_structure_recognition.table_dataset import TableDataset

# 导入步骤5.1的工具函数
from modules.utils.lore_tsr.lore_image_utils import (
    get_affine_transform,
    get_affine_transform_upper_left,
    affine_transform,
    color_aug,
    flip
)

# 设置日志
logger = logging.getLogger(__name__)


class LoreTsrDataset(TableDataset):
    """
    LORE-TSR数据集类，继承TableDataset基类

    迭代5步骤5.2：数据集基础框架
    实现WTW分布式标注格式到LORE格式的完整转换

    主要功能：
    1. 继承TableDataset的数据加载基础设施
    2. 实现WTW到LORE格式的准确转换
    3. 支持LORE-TSR的目标生成需求
    4. 与train-anything框架深度集成
    """

    def __init__(self, config, mode='train'):
        """
        初始化LORE-TSR数据集

        Args:
            config: OmegaConf配置对象，包含数据和模型配置
            mode: 数据集模式 ('train', 'val', 'test')
        """
        # 从配置中获取数据目录列表（采用与Cycle-CenterNet-MS相同的配置方式）
        data_dirs = config.data.paths.train_data_dir if mode == 'train' else config.data.paths.val_data_dir

        # 调用父类初始化，复用TableDataset的数据加载基础设施
        super().__init__(
            data_root=data_dirs,  # 传递目录列表
            mode=mode,
            target_size=(config.data.processing.image_size[0], config.data.processing.image_size[1]),
            debug=config.basic.get('debug', 0) > 0,  # 使用basic.debug，转换为布尔值
            max_samples=config.data.processing.get('max_samples', None)
        )

        # 保存配置和模式
        self.config = config
        self.mode = mode

        # 设置LORE-TSR特定参数
        self.input_h = config.data.processing.image_size[0]
        self.input_w = config.data.processing.image_size[1]
        self.down_ratio = config.data.processing.down_ratio
        self.num_classes = config.model.heads.hm
        self.output_h = self.input_h // self.down_ratio
        self.output_w = self.input_w // self.down_ratio

        # LORE-TSR特定配置
        self.max_objs = config.data.processing.get('max_objs', 500)
        self.gaussian_iou = config.data.processing.get('gaussian_iou', 0.7)

        # 步骤5.4：添加LORE-TSR目标生成配置
        # 直接复制ctdet.py的目标参数配置
        self.max_cors = config.data.targets.get('max_cors', 1200)  # 最大角点数
        self.max_pairs = config.data.targets.get('max_pairs', 900)  # 最大配对数
        self.num_classes = config.model.heads.hm  # 类别数（包含角点类）
        self.mse_loss = config.data.targets.get('mse_loss', False)  # 是否使用MSE损失
        self.hm_gauss = config.data.targets.get('hm_gauss', 2)  # MSE模式下的高斯半径

        # 步骤5.3：添加LORE-TSR数据处理pipeline配置
        # 直接复制ctdet.py的配置参数
        self.upper_left = config.data.processing.get('upper_left', False)
        self.keep_res = config.data.processing.get('keep_res', False)
        self.pad = config.data.processing.get('pad', 31)
        self.not_rand_crop = config.data.processing.get('not_rand_crop', True)

        # 数据增强参数（对应ctdet.py第214-218行）
        self.scale = config.data.augmentation.get('scale', 0.4)
        self.shift = config.data.augmentation.get('shift', 0.1)
        self.rotate = config.data.augmentation.get('rotate', 0)
        self.flip_prob = config.data.augmentation.get('flip', 0.5)
        self.no_color_aug = config.data.augmentation.get('no_color_aug', False)

        # LORE-TSR标准参数（对应ctdet.py第359行）
        self.mean = np.array([0.408, 0.447, 0.470], dtype=np.float32).reshape(1, 1, 3)
        self.std = np.array([0.289, 0.274, 0.278], dtype=np.float32).reshape(1, 1, 3)

        # 颜色增强参数（对应ctdet.py第356行）
        self._eig_val = np.array([0.2141788, 0.01817699, 0.00341571], dtype=np.float32)
        self._eig_vec = np.array([[-0.58752847, -0.69563484, 0.41340352],
                                [-0.5832747, 0.00994535, -0.81221408],
                                [-0.56089297, 0.71832671, 0.41158938]], dtype=np.float32)
        self._data_rng = np.random.RandomState(123)

        logger.info(f"LoreTsrDataset初始化完成: mode={mode}, "
                   f"input_size=({self.input_h}, {self.input_w}), "
                   f"output_size=({self.output_h}, {self.output_w}), "
                   f"down_ratio={self.down_ratio}, num_classes={self.num_classes}")

        # 验证配置
        self._validate_config()

    def _validate_config(self):
        """验证配置参数的有效性"""
        assert self.input_h > 0 and self.input_w > 0, "图像尺寸必须为正数"
        assert self.down_ratio > 0, "下采样比例必须为正数"
        assert self.num_classes > 0, "类别数必须为正数"
        assert self.output_h > 0 and self.output_w > 0, "输出尺寸必须为正数"

        logger.debug(f"配置验证通过: input=({self.input_h}, {self.input_w}), "
                    f"output=({self.output_h}, {self.output_w}), "
                    f"down_ratio={self.down_ratio}")

    def _convert_wtw_to_lore_format(self, annotation: Dict) -> Dict:
        """
        将WTW分布式标注格式转换为LORE格式

        WTW格式：
        - bbox: {p1: [x1,y1], p2: [x2,y2], p3: [x3,y3], p4: [x4,y4]}
        - lloc: {start_row: int, end_row: int, start_col: int, end_col: int}

        LORE格式：
        - segmentation: [[x1,y1,x2,y2,x3,y3,x4,y4]] (展平的四点坐标)
        - logic_axis: [start_row, end_row, start_col, end_col]
        - category_id: 1 (固定为前景类)

        Args:
            annotation: WTW格式的标注数据

        Returns:
            Dict: LORE格式的标注数据
        """
        lore_annotations = []

        if 'annotations' not in annotation:
            logger.warning("标注中缺少annotations字段")
            return {'annotations': []}

        for ann in annotation['annotations']:
            try:
                lore_ann = self._convert_single_annotation(ann)
                if lore_ann is not None:
                    lore_annotations.append(lore_ann)
            except Exception as e:
                logger.warning(f"转换单个标注失败: {e}")
                continue

        return {
            'annotations': lore_annotations,
            'image': annotation.get('image', {}),
            'info': annotation.get('info', {})
        }

    def _convert_single_annotation(self, ann: Dict) -> Optional[Dict]:
        """
        转换单个标注对象

        Args:
            ann: 单个WTW格式标注

        Returns:
            Optional[Dict]: LORE格式标注，转换失败时返回None
        """
        # 检查必需字段
        if 'bbox' not in ann or 'lloc' not in ann:
            logger.warning(f"标注缺少必需字段: {ann.keys()}")
            return None

        bbox = ann['bbox']
        lloc = ann['lloc']

        # 验证bbox格式
        required_points = ['p1', 'p2', 'p3', 'p4']
        if not all(point in bbox for point in required_points):
            logger.warning(f"bbox缺少必需点: {bbox.keys()}")
            return None

        # 验证lloc格式
        required_lloc_fields = ['start_row', 'end_row', 'start_col', 'end_col']
        if not all(field in lloc for field in required_lloc_fields):
            logger.warning(f"lloc缺少必需字段: {lloc.keys()}")
            return None

        try:
            # 转换bbox为segmentation（展平的四点坐标）
            segmentation = []
            for point_name in required_points:
                point = bbox[point_name]
                if not isinstance(point, (list, tuple)) or len(point) != 2:
                    logger.warning(f"无效的点坐标: {point_name}={point}")
                    return None
                segmentation.extend([float(point[0]), float(point[1])])

            # 转换lloc为logic_axis
            logic_axis = [
                int(lloc['start_row']),
                int(lloc['end_row']),
                int(lloc['start_col']),
                int(lloc['end_col'])
            ]

            # 构建LORE格式标注
            lore_ann = {
                'segmentation': [segmentation],  # LORE格式要求嵌套列表
                'logic_axis': logic_axis,
                'category_id': 1,  # 固定为前景类
                'id': ann.get('id', 0),
                'area': self._calculate_polygon_area(segmentation),
                'iscrowd': 0
            }

            return lore_ann

        except (ValueError, TypeError) as e:
            logger.warning(f"数值转换失败: {e}")
            return None

    def _calculate_polygon_area(self, segmentation: List[float]) -> float:
        """
        计算多边形面积（使用鞋带公式）

        Args:
            segmentation: 展平的坐标列表 [x1,y1,x2,y2,x3,y3,x4,y4]

        Returns:
            float: 多边形面积
        """
        if len(segmentation) != 8:
            return 0.0

        # 重新组织为点对
        points = [(segmentation[i], segmentation[i+1]) for i in range(0, 8, 2)]

        # 鞋带公式
        area = 0.0
        n = len(points)
        for i in range(n):
            j = (i + 1) % n
            area += points[i][0] * points[j][1]
            area -= points[j][0] * points[i][1]

        return abs(area) / 2.0

    def _apply_image_preprocessing(self, img: np.ndarray) -> Tuple[np.ndarray, np.ndarray, int, int]:
        """
        实现基础图像预处理逻辑（对应ctdet.py第185-198行）

        Args:
            img: 输入图像 [H, W, C]

        Returns:
            Tuple: (中心点c, 缩放s, input_h, input_w)
        """
        height, width = img.shape[0], img.shape[1]

        # 对应ctdet.py第187-190行：中心点计算
        if self.upper_left:
            c = np.array([0, 0], dtype=np.float32)
        else:
            c = np.array([img.shape[1] / 2., img.shape[0] / 2.], dtype=np.float32)

        # 对应ctdet.py第192-198行：尺寸处理
        if self.keep_res:
            input_h = (height | self.pad)  # + 1
            input_w = (width | self.pad)   # + 1
            s = np.array([input_w, input_h], dtype=np.float32)
        else:
            s = max(img.shape[0], img.shape[1]) * 1.0
            input_h, input_w = self.input_h, self.input_w

        return c, s, input_h, input_w

    def _get_border(self, border: int, size: int) -> int:
        """
        计算边界（直接复制ctdet.py第29-33行）

        Args:
            border: 边界值
            size: 尺寸

        Returns:
            int: 计算后的边界
        """
        i = 1
        while size - border // i <= border // i:
            i *= 2
        return border // i

    def _apply_random_augmentation(self, c: np.ndarray, s: float, img_shape: Tuple[int, int],
                                 is_train: bool = True) -> Tuple[np.ndarray, float, int]:
        """
        实现训练时随机数据增强（对应ctdet.py第201-223行）

        Args:
            c: 中心点
            s: 缩放因子
            img_shape: 图像形状 (height, width)
            is_train: 是否为训练模式

        Returns:
            Tuple: (增强后中心点, 增强后缩放, 旋转角度)
        """
        flipped = False
        rot = 0

        if is_train:
            if not self.not_rand_crop:
                # 对应ctdet.py第203-211行：随机裁剪模式
                if self.upper_left:
                    c = np.array([0, 0], dtype=np.float32)
                else:
                    s = s * np.random.choice(np.arange(0.6, 1.4, 0.1))
                    w_border = self._get_border(128, img_shape[1])
                    h_border = self._get_border(128, img_shape[0])
                    c[0] = np.random.randint(low=w_border, high=img_shape[1] - w_border)
                    c[1] = np.random.randint(low=h_border, high=img_shape[0] - h_border)
            else:
                # 对应ctdet.py第214-218行：标准数据增强模式
                sf = self.scale
                cf = self.shift
                c[0] += s * np.clip(np.random.randn() * cf, -2 * cf, 2 * cf)
                c[1] += s * np.clip(np.random.randn() * cf, -2 * cf, 2 * cf)
                s = s * np.clip(np.random.randn() * sf + 1, 1 - sf, 1 + sf)

        # 对应ctdet.py第220-223行：旋转增强
        if self.rotate == 1:
            print('----rotate----')
            rot = np.random.randint(-15, 15)

        return c, s, rot

    def _apply_affine_transform(self, img: np.ndarray, c: np.ndarray, s: float, rot: int,
                              input_h: int, input_w: int) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        实现仿射变换和图像变换（对应ctdet.py第225-238行）

        Args:
            img: 输入图像
            c: 中心点
            s: 缩放因子
            rot: 旋转角度
            input_h: 输入高度
            input_w: 输入宽度

        Returns:
            Tuple: (变换后图像, 输入变换矩阵, 输出变换矩阵)
        """
        # 对应ctdet.py第225-226行：计算输出尺寸
        output_h = input_h // self.down_ratio
        output_w = input_w // self.down_ratio

        # 对应ctdet.py第228-236行：计算变换矩阵
        if self.upper_left:
            trans_input = get_affine_transform_upper_left(c, s, rot, [input_w, input_h])
            trans_output = get_affine_transform_upper_left(c, s, rot, [output_w, output_h])
        else:
            trans_input = get_affine_transform(c, s, rot, [input_w, input_h])
            trans_output = get_affine_transform(c, s, rot, [output_w, output_h])

        # 对应ctdet.py第262行：应用仿射变换
        inp = cv2.warpAffine(img, trans_input, (input_w, input_h), flags=cv2.INTER_LINEAR)

        return inp, trans_input, trans_output

    def _apply_image_postprocessing(self, inp: np.ndarray, is_train: bool = True) -> torch.Tensor:
        """
        实现颜色增强和图像后处理（对应ctdet.py第354-360行）

        Args:
            inp: 输入图像 [H, W, C]
            is_train: 是否为训练模式

        Returns:
            torch.Tensor: 处理后的图像 [C, H, W]
        """
        # 对应ctdet.py第354行：归一化到[0,1]
        inp = (inp.astype(np.float32) / 255.)

        # 对应ctdet.py第355-356行：颜色增强（仅训练模式且未禁用）
        if is_train and not self.no_color_aug:
            color_aug(self._data_rng, inp, self._eig_val, self._eig_vec)

        # 对应ctdet.py第359行：标准化
        inp = (inp - self.mean) / self.std

        # 对应ctdet.py第360行：转换为CHW格式
        inp = inp.transpose(2, 0, 1)

        return torch.from_numpy(inp).float()

    def _initialize_lore_targets(self, output_h: int, output_w: int) -> Dict[str, np.ndarray]:
        """
        实现基础目标张量初始化（对应ctdet.py第240-263行）

        Args:
            output_h: 输出特征图高度
            output_w: 输出特征图宽度

        Returns:
            Dict: 初始化的目标张量字典
        """
        # 对应ctdet.py第240-255行：初始化所有目标张量
        targets = {
            # 核心目标张量
            'hm': np.zeros((self.num_classes, output_h, output_w), dtype=np.float32),  # 热力图
            'wh': np.zeros((self.max_objs, 8), dtype=np.float32),  # 边界框（4个角点偏移）
            'reg': np.zeros((self.max_objs*5, 2), dtype=np.float32),  # 回归偏移
            'st': np.zeros((self.max_cors, 8), dtype=np.float32),  # 结构张量
            'hm_ctxy': np.zeros((self.max_objs, 2), dtype=np.float32),  # 中心坐标
            'logic': np.zeros((self.max_objs, 4), dtype=np.float32),  # 逻辑轴（log_ax）

            # 索引和掩码张量
            'hm_ind': np.zeros((self.max_objs,), dtype=np.int64),  # 热力图索引
            'hm_mask': np.zeros((self.max_objs,), dtype=np.uint8),  # 热力图掩码
            'mk_ind': np.zeros((self.max_cors,), dtype=np.int64),  # 角点索引
            'mk_mask': np.zeros((self.max_cors,), dtype=np.uint8),  # 角点掩码
            'reg_ind': np.zeros((self.max_objs*5,), dtype=np.int64),  # 回归索引
            'reg_mask': np.zeros((self.max_objs*5,), dtype=np.uint8),  # 回归掩码

            # 复杂关系张量
            'ctr_cro_ind': np.zeros((self.max_objs*4,), dtype=np.int64),  # 中心-角点索引
            'cc_match': np.zeros((self.max_objs, 4), dtype=np.int64),  # 中心-角点匹配
            'h_pair_ind': np.zeros((self.max_pairs,), dtype=np.int64),  # 水平配对索引
            'v_pair_ind': np.zeros((self.max_pairs,), dtype=np.int64),  # 垂直配对索引
        }

        return targets

    def _judge(self, corner_points: np.ndarray) -> bool:
        """
        判断角点是否有效（直接复制ctdet.py的_judge方法）

        Args:
            corner_points: 角点坐标数组 [x1,y1,x2,y2,x3,y3,x4,y4]

        Returns:
            bool: 角点是否有效
        """
        # 简单的有效性检查：确保角点不重合
        x_coords = corner_points[::2]  # [x1, x2, x3, x4]
        y_coords = corner_points[1::2]  # [y1, y2, y3, y4]

        # 检查是否有足够的变化
        x_range = np.max(x_coords) - np.min(x_coords)
        y_range = np.max(y_coords) - np.min(y_coords)

        return x_range > 1.0 and y_range > 1.0

    def _generate_heatmap_targets(self, hm: np.ndarray, corner_points: np.ndarray,
                                cls_id: int) -> Tuple[np.ndarray, int]:
        """
        实现热力图生成逻辑（对应ctdet.py第290-305行）

        Args:
            hm: 热力图张量 [num_classes, H, W]
            corner_points: 角点坐标 [x1,y1,x2,y2,x3,y3,x4,y4]
            cls_id: 类别ID

        Returns:
            Tuple: (中心点坐标, 高斯半径)
        """
        # 对应ctdet.py第291-295行：计算包围盒
        x_coords = corner_points[::2]  # [x1, x2, x3, x4]
        y_coords = corner_points[1::2]  # [y1, y2, y3, y4]

        maxx = max(x_coords)
        minx = min(x_coords)
        maxy = max(y_coords)
        miny = min(y_coords)
        h, w = maxy - miny, maxx - minx

        if h > 0 and w > 0:
            # 对应ctdet.py第298-300行：计算高斯半径
            from modules.utils.lore_tsr.lore_image_utils import gaussian_radius
            radius = gaussian_radius((math.ceil(h), math.ceil(w)))
            radius = max(0, int(radius))
            radius = self.hm_gauss if self.mse_loss else radius

            # 对应ctdet.py第302-303行：计算中心点
            ct = np.array([(maxx + minx) / 2.0, (maxy + miny) / 2.0], dtype=np.float32)
            ct_int = ct.astype(np.int32)

            # 对应ctdet.py第305行：绘制高斯热力图
            from modules.utils.lore_tsr.lore_image_utils import draw_umich_gaussian, draw_msra_gaussian
            draw_gaussian = draw_msra_gaussian if self.mse_loss else draw_umich_gaussian
            draw_gaussian(hm[cls_id], ct_int, radius)

            return ct, radius

        return np.array([0.0, 0.0], dtype=np.float32), 0

    def _generate_bbox_targets(self, targets: Dict, corner_points: np.ndarray, center: np.ndarray,
                             obj_idx: int, output_w: int, output_h: int, cor_list: List[str],
                             num_classes: int) -> None:
        """
        实现边界框和偏移目标生成（对应ctdet.py第306-342行）

        Args:
            targets: 目标张量字典
            corner_points: 角点坐标 [x1,y1,x2,y2,x3,y3,x4,y4]
            center: 中心点坐标 [cx, cy]
            obj_idx: 对象索引
            output_w: 输出宽度
            output_h: 输出高度
            cor_list: 角点列表（用于去重）
            num_classes: 类别数
        """
        ct_int = center.astype(np.int32)

        # 对应ctdet.py第307-330行：处理4个角点
        for i in range(4):
            # 获取角点坐标
            cor = np.array([corner_points[2*i], corner_points[2*i+1]], dtype=np.float32)
            cor_int = cor.astype(np.int32)
            cor_key = f"{cor_int[0]}_{cor_int[1]}"

            if cor_key not in cor_list:
                # 新角点
                cor_num = len(cor_list)
                cor_list.append(cor_key)

                # 对应ctdet.py第316-324行：设置角点目标
                targets['reg'][self.max_objs + cor_num] = np.array([
                    abs(cor[0] - cor_int[0]),
                    abs(cor[1] - cor_int[1])
                ])
                targets['mk_ind'][cor_num] = cor_int[1] * output_w + cor_int[0]
                targets['cc_match'][obj_idx][i] = targets['mk_ind'][cor_num]
                targets['reg_ind'][self.max_objs + cor_num] = cor_int[1] * output_w + cor_int[0]
                targets['mk_mask'][cor_num] = 1
                targets['reg_mask'][self.max_objs + cor_num] = 1

                # 绘制角点热力图（角点类）
                from modules.utils.lore_tsr.lore_image_utils import draw_umich_gaussian
                draw_umich_gaussian(targets['hm'][num_classes-1], cor_int, 2)

                # 设置结构张量
                targets['st'][cor_num][i*2:(i+1)*2] = np.array([cor[0] - center[0], cor[1] - center[1]])
                targets['ctr_cro_ind'][4*obj_idx + i] = cor_num * 4 + i
            else:
                # 已存在的角点
                index_of_key = cor_list.index(cor_key)
                targets['cc_match'][obj_idx][i] = targets['mk_ind'][index_of_key]
                targets['st'][index_of_key][i*2:(i+1)*2] = np.array([cor[0] - center[0], cor[1] - center[1]])
                targets['ctr_cro_ind'][4*obj_idx + i] = index_of_key * 4 + i

        # 对应ctdet.py第332-342行：设置边界框和中心点目标
        targets['wh'][obj_idx] = [
            center[0] - 1.0 * corner_points[0], center[1] - 1.0 * corner_points[1],  # 到第1个角点
            center[0] - 1.0 * corner_points[2], center[1] - 1.0 * corner_points[3],  # 到第2个角点
            center[0] - 1.0 * corner_points[4], center[1] - 1.0 * corner_points[5],  # 到第3个角点
            center[0] - 1.0 * corner_points[6], center[1] - 1.0 * corner_points[7]   # 到第4个角点
        ]

        targets['hm_ind'][obj_idx] = ct_int[1] * output_w + ct_int[0]
        targets['hm_mask'][obj_idx] = 1
        targets['reg_ind'][obj_idx] = ct_int[1] * output_w + ct_int[0]
        targets['reg_mask'][obj_idx] = 1
        targets['reg'][obj_idx] = center - ct_int
        targets['hm_ctxy'][obj_idx] = [center[0], center[1]]

    def _generate_logic_targets(self, targets: Dict, logic_axis: List[int], obj_idx: int) -> None:
        """
        实现逻辑轴目标生成（对应ctdet.py第344行）

        Args:
            targets: 目标张量字典
            logic_axis: 逻辑轴坐标 [start_row, end_row, start_col, end_col]
            obj_idx: 对象索引
        """
        # 对应ctdet.py第344行：设置逻辑轴目标
        if len(logic_axis) >= 4:
            targets['logic'][obj_idx] = [
                logic_axis[0],  # start_row
                logic_axis[1],  # end_row
                logic_axis[2],  # start_col
                logic_axis[3]   # end_col
            ]

    def _generate_lore_targets(self, lore_anns: Dict, trans_output: np.ndarray,
                             output_w: int, output_h: int, flipped: bool = False) -> Dict:
        """
        生成完整的LORE-TSR目标张量（对应ctdet.py第240-363行的完整逻辑）

        Args:
            lore_anns: LORE格式的标注数据
            trans_output: 输出变换矩阵
            output_w: 输出宽度
            output_h: 输出高度
            flipped: 是否翻转

        Returns:
            Dict: 完整的LORE-TSR目标张量
        """
        # 步骤5.4.1：初始化目标张量
        targets = self._initialize_lore_targets(output_h, output_w)

        # 获取标注列表
        anns = lore_anns.get('annotations', [])
        num_objs = len(anns)

        if num_objs == 0:
            # 没有标注，返回空目标
            return self._convert_targets_to_torch(targets)

        # 角点列表（用于去重）
        cor_list = []

        # 对应ctdet.py第264-345行：处理每个标注对象
        for k in range(min(num_objs, self.max_objs)):
            ann = anns[k]

            # 获取分割掩码（4个角点）
            if 'segmentation' not in ann or len(ann['segmentation']) == 0:
                continue

            seg_mask = ann['segmentation'][0]
            if len(seg_mask) < 8:
                continue

            # 对应ctdet.py第268-271行：提取角点坐标
            corner_points = np.array([
                seg_mask[0], seg_mask[1],  # x1, y1
                seg_mask[2], seg_mask[3],  # x2, y2
                seg_mask[4], seg_mask[5],  # x3, y3
                seg_mask[6], seg_mask[7]   # x4, y4
            ], dtype=np.float32)

            # 获取类别ID
            cls_id = int(ann.get('category_id', 1)) - 1  # 转换为0-based索引
            if cls_id < 0 or cls_id >= self.num_classes - 1:  # 预留最后一个类别给角点
                cls_id = 0

            # 对应ctdet.py第278-289行：处理翻转和仿射变换
            if flipped:
                # 翻转处理（如果需要）
                width = output_w * self.down_ratio  # 恢复到原始尺寸
                corner_points[[0,2,4,6]] = width - corner_points[[2,0,6,4]] - 1

            # 应用仿射变换
            from modules.utils.lore_tsr.lore_image_utils import affine_transform
            for i in range(4):
                corner_points[2*i:2*i+2] = affine_transform(corner_points[2*i:2*i+2], trans_output)

            # 边界裁剪
            corner_points[[0,2,4,6]] = np.clip(corner_points[[0,2,4,6]], 0, output_w - 1)
            corner_points[[1,3,5,7]] = np.clip(corner_points[[1,3,5,7]], 0, output_h - 1)

            # 判断角点有效性
            if not self._judge(corner_points):
                continue

            # 步骤5.4.2：生成热力图目标
            center, radius = self._generate_heatmap_targets(targets['hm'], corner_points, cls_id)
            if radius == 0:
                continue

            # 步骤5.4.3：生成边界框和偏移目标
            self._generate_bbox_targets(targets, corner_points, center, k, output_w, output_h,
                                      cor_list, self.num_classes)

            # 步骤5.4.4：生成逻辑轴目标
            if 'logic_axis' in ann:
                self._generate_logic_targets(targets, ann['logic_axis'], k)

        return self._convert_targets_to_torch(targets)

    def _convert_targets_to_torch(self, targets: Dict[str, np.ndarray]) -> Dict[str, torch.Tensor]:
        """
        将numpy目标张量转换为torch张量

        Args:
            targets: numpy格式的目标张量

        Returns:
            Dict: torch格式的目标张量
        """
        torch_targets = {}
        for key, value in targets.items():
            torch_targets[key] = torch.from_numpy(value)

        return torch_targets

    def _prepare_lore_targets(self, lore_annotation: Dict, trans_output: np.ndarray,
                            output_w: int, output_h: int, flipped: bool = False) -> Dict:
        """
        准备LORE-TSR特定的目标格式

        步骤5.4：完整的LORE-TSR目标生成逻辑

        Args:
            lore_annotation: LORE格式标注
            trans_output: 输出变换矩阵
            output_w: 输出宽度
            output_h: 输出高度
            flipped: 是否翻转

        Returns:
            Dict: 完整的LORE-TSR目标格式
        """
        try:
            # 使用步骤5.4的完整目标生成逻辑
            targets = self._generate_lore_targets(lore_annotation, trans_output, output_w, output_h, flipped)
            return targets
        except Exception as e:
            # 错误处理：创建默认目标
            logger.warning(f"目标生成失败，使用默认目标: {e}")
            return self._create_default_targets_torch()

    def _create_default_targets(self) -> Dict:
        """
        创建默认目标（当目标准备失败时使用）

        Returns:
            Dict: 默认的LORE-TSR目标格式
        """
        output_h, output_w = self.output_h, self.output_w
        max_objs = self.max_objs

        targets = {
            'hm': torch.zeros(self.num_classes, output_h, output_w, dtype=torch.float32),
            'wh': torch.zeros(max_objs, 8, dtype=torch.float32),
            'reg': torch.zeros(max_objs, 2, dtype=torch.float32),
            'reg_mask': torch.zeros(max_objs, dtype=torch.float32),
            'ind': torch.zeros(max_objs, dtype=torch.long),
            'num_objs': torch.tensor(0, dtype=torch.long)
        }

        logger.debug(f"创建默认目标: hm={targets['hm'].shape}, "
                    f"wh={targets['wh'].shape}, max_objs={max_objs}")

        return targets

    def _create_default_targets_torch(self) -> Dict:
        """
        创建torch格式的默认目标（当目标生成失败时使用）

        Returns:
            Dict: 默认的torch格式LORE-TSR目标
        """
        output_h, output_w = self.output_h, self.output_w

        targets = {
            'hm': torch.zeros(self.num_classes, output_h, output_w, dtype=torch.float32),
            'wh': torch.zeros(self.max_objs, 8, dtype=torch.float32),
            'reg': torch.zeros(self.max_objs*5, 2, dtype=torch.float32),
            'st': torch.zeros(self.max_cors, 8, dtype=torch.float32),
            'hm_ctxy': torch.zeros(self.max_objs, 2, dtype=torch.float32),
            'logic': torch.zeros(self.max_objs, 4, dtype=torch.float32),
            'hm_ind': torch.zeros(self.max_objs, dtype=torch.long),
            'hm_mask': torch.zeros(self.max_objs, dtype=torch.uint8),
            'mk_ind': torch.zeros(self.max_cors, dtype=torch.long),
            'mk_mask': torch.zeros(self.max_cors, dtype=torch.uint8),
            'reg_ind': torch.zeros(self.max_objs*5, dtype=torch.long),
            'reg_mask': torch.zeros(self.max_objs*5, dtype=torch.uint8),
            'ctr_cro_ind': torch.zeros(self.max_objs*4, dtype=torch.long),
            'cc_match': torch.zeros(self.max_objs, 4, dtype=torch.long),
            'h_pair_ind': torch.zeros(self.max_pairs, dtype=torch.long),
            'v_pair_ind': torch.zeros(self.max_pairs, dtype=torch.long),
        }

        logger.debug(f"创建默认torch目标: hm={targets['hm'].shape}, "
                    f"wh={targets['wh'].shape}, max_objs={self.max_objs}")

        return targets

    def __getitem__(self, index: int) -> Dict[str, Any]:
        """
        获取单个样本 - 完整的LORE-TSR数据处理pipeline

        步骤5.3：集成完整的ctdet.py数据处理逻辑

        Args:
            index: 样本索引

        Returns:
            Dict: 包含处理后图像和目标的样本字典
        """
        try:
            # 调用父类获取基础样本（图像和WTW格式标注）
            sample = super().__getitem__(index)

            # 获取原始图像（从sample['image']转换为numpy格式）
            if isinstance(sample['image'], torch.Tensor):
                # 如果是tensor，转换为numpy [H, W, C]
                img = sample['image'].permute(1, 2, 0).numpy()
                if img.dtype != np.uint8:
                    img = (img * 255).astype(np.uint8)
            else:
                # 如果已经是numpy，直接使用
                img = sample['image']

            # 确保图像是BGR格式（OpenCV标准）
            if img.shape[2] == 3:
                img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)

            # 步骤5.3.1：基础图像预处理
            c, s, input_h, input_w = self._apply_image_preprocessing(img)

            # 步骤5.3.2：随机数据增强（仅训练模式）
            is_train = (self.mode == 'train')
            c, s, rot = self._apply_random_augmentation(c, s, img.shape[:2], is_train)

            # 步骤5.3.3：仿射变换和图像变换
            inp, trans_input, trans_output = self._apply_affine_transform(
                img, c, s, rot, input_h, input_w)

            # 步骤5.3.4：颜色增强和图像后处理
            processed_img = self._apply_image_postprocessing(inp, is_train)

            # 准备LORE格式的标注和目标
            if 'annotation' in sample:
                lore_annotation = self._convert_wtw_to_lore_format(sample['annotation'])
                # 使用步骤5.4的完整目标生成逻辑
                lore_targets = self._prepare_lore_targets(
                    lore_annotation, trans_output,
                    input_w // self.down_ratio, input_h // self.down_ratio,
                    flipped=False  # TODO: 根据实际翻转状态设置
                )
            else:
                logger.warning(f"样本{index}缺少annotation字段")
                lore_targets = self._create_default_targets_torch()

            # 构建返回结果（对应ctdet.py第362-379行的返回格式）
            ret = {
                'input': processed_img,  # 处理后的图像 [C, H, W]
                'image_id': sample.get('image_id', index),
                'meta': {
                    'c': c,
                    's': s,
                    'rot': rot,
                    'img_id': sample.get('image_id', index),
                    'trans_input': trans_input,
                    'trans_output': trans_output,
                    'input_size': (input_h, input_w),
                    'output_size': (input_h // self.down_ratio, input_w // self.down_ratio)
                }
            }

            # 添加目标信息
            ret.update(lore_targets)

            # 添加调试信息
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"样本{index}: input_shape={processed_img.shape}, "
                           f"center={c}, scale={s}, rotation={rot}")

            return ret

        except Exception as e:
            logger.error(f"获取样本{index}失败: {e}")
            # 返回默认样本
            return self._create_default_sample_lore_format()

    def _log_sample_info(self, sample: Dict, index: int):
        """记录样本信息（调试用）"""
        if 'targets' in sample:
            targets = sample['targets']
            logger.debug(f"样本{index}: "
                        f"hm_shape={targets.get('hm', torch.empty(0)).shape}, "
                        f"num_objs={targets.get('num_objs', 0)}")

    def _create_default_sample(self) -> Dict[str, Any]:
        """创建默认样本（当样本加载失败时使用）"""
        return {
            'image': torch.zeros(3, self.input_h, self.input_w, dtype=torch.float32),
            'targets': self._create_default_targets(),
            'annotation': {'annotations': []},
            'image_id': 0
        }

    def _create_default_sample_lore_format(self) -> Dict[str, Any]:
        """创建LORE格式的默认样本（当样本加载失败时使用）"""
        output_h = self.input_h // self.down_ratio
        output_w = self.input_w // self.down_ratio

        default_targets = self._create_default_targets()

        return {
            'input': torch.zeros(3, self.input_h, self.input_w, dtype=torch.float32),
            'image_id': 0,
            'meta': {
                'c': np.array([self.input_w / 2., self.input_h / 2.], dtype=np.float32),
                's': max(self.input_h, self.input_w) * 1.0,
                'rot': 0,
                'img_id': 0,
                'trans_input': np.eye(2, 3, dtype=np.float32),
                'trans_output': np.eye(2, 3, dtype=np.float32),
                'input_size': (self.input_h, self.input_w),
                'output_size': (output_h, output_w)
            },
            **default_targets
        }

    def __len__(self) -> int:
        """返回数据集大小"""
        return super().__len__()

    def get_sample_info(self, index: int) -> Dict[str, Any]:
        """
        获取样本信息（不加载图像）

        Args:
            index: 样本索引

        Returns:
            Dict: 样本信息
        """
        try:
            # 获取基础信息
            info = super().get_sample_info(index) if hasattr(super(), 'get_sample_info') else {}

            # 添加LORE-TSR特定信息
            info.update({
                'dataset_type': 'LoreTsrDataset',
                'mode': self.mode,
                'input_size': (self.input_h, self.input_w),
                'output_size': (self.output_h, self.output_w),
                'down_ratio': self.down_ratio,
                'num_classes': self.num_classes
            })

            return info

        except Exception as e:
            logger.warning(f"获取样本{index}信息失败: {e}")
            return {'error': str(e)}
