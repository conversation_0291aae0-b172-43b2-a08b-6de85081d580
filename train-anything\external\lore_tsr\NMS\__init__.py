"""
NMS (Non-Maximum Suppression) 模块
从LORE-TSR原样复制的完整实现，包含Cython高性能实现和Shapely几何回退
"""

# 尝试导入编译后的Cython NMS实现
try:
    from .nms import nms, soft_nms, soft_nms_39, soft_nms_merge
    NMS_CYTHON_AVAILABLE = True
    print("✅ NMS Cython实现加载成功")
    __all__ = ['nms', 'soft_nms', 'soft_nms_39', 'soft_nms_merge']
except ImportError as e:
    print(f"⚠️  NMS Cython实现导入失败: {e}")
    print("📝 请编译NMS: cd external/lore_tsr/NMS && python setup.py build_ext --inplace")
    NMS_CYTHON_AVAILABLE = False
    __all__ = []

# 导入Shapely几何NMS实现作为备选
try:
    from .shapelyNMS import delet_min_first, delet_min
    SHAPELY_NMS_AVAILABLE = True
    __all__.extend(['delet_min_first', 'delet_min'])
except ImportError as e:
    print(f"⚠️  Shapely NMS实现导入失败: {e}")
    SHAPELY_NMS_AVAILABLE = False

# 导出可用性标志
__all__.extend(['NMS_CYTHON_AVAILABLE', 'SHAPELY_NMS_AVAILABLE'])
