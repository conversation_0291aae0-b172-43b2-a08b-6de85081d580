# LORE-TSR 迁移编码计划 - 迭代3步骤3.1

## 📋 计划概述

**当前迭代**: 迭代3 - 基础训练循环  
**步骤标识**: 步骤3.1 - 建立最小训练循环框架  
**迁移策略**: 重构适配框架入口 + 复制保留核心算法  
**核心目标**: 实现基于accelerate的最小训练循环，使用简化损失函数和虚拟数据，确保训练能够启动并运行

## 🗺️ 动态迁移蓝图

### 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | ✅ **已完成** |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | 🔄 **进行中** |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留：模型工厂函数 | 迭代2 | **复杂** | ✅ **已完成** |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | 简单 | 🔄 **进行中** |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留：主要骨干网络 | 迭代2 | 简单 | ✅ **已完成** |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配：数据集适配器 | 迭代5 | **复杂** | 🔄 **进行中** |
| `src/lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留：后处理工具 | 迭代11 | 简单 | `未开始` |
| `src/lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离：可变形卷积 | 迭代7 | 简单 | `未开始` |
| `src/lib/models/networks/NMS/` | `external/lore_tsr/NMS/` | 复制隔离：非极大值抑制 | 迭代7 | 简单 | `未开始` |

**状态说明:**
- ✅ **已完成**: 迭代1,2已完成的文件
- 🔄 **进行中**: 当前步骤3.1正在处理的文件
- `未开始`: 后续迭代将处理的文件

### 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代3步骤3.1 - 最小训练循环框架

    subgraph "Source: LORE-TSR/src/lib/losses.py"
        direction LR
        loss_focal["FocalLoss实现"]
        loss_reg["RegL1Loss实现"]
        loss_multi["多任务损失组合"]
    end

    subgraph "Source: LORE-TSR/src/main.py"
        direction LR
        main_loop["训练循环逻辑"]
        main_data["数据加载逻辑"]
        main_loss["损失计算逻辑"]
    end

    subgraph "Target: train-anything (步骤3.1)"
        T1["networks/lore_tsr/lore_tsr_loss.py"]
        T2["modules/utils/lore_tsr/dummy_data_utils.py"]
        T3["training_loops/.../train_lore_tsr.py (完善)"]
    end

    %% 迁移映射
    loss_focal -- "Copy & Simplify" --> T1
    loss_reg -- "Copy & Simplify" --> T1
    loss_multi -- "Copy & Simplify" --> T1

    main_data -- "Create Dummy Version" --> T2
    main_loop -- "Refactor to accelerate" --> T3
    main_loss -- "Integrate with T1" --> T3

    %% 依赖关系
    T1 -.-> T3
    T2 -.-> T3
```

## 📁 目标目录结构树

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                     # ✅ 已存在
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                        # 🔄 待完善
├── networks/lore_tsr/
│   ├── __init__.py                              # ✅ 已存在
│   ├── lore_tsr_model.py                        # ✅ 已存在
│   ├── lore_tsr_loss.py                         # 🆕 待创建
│   ├── backbones/                               # ✅ 已存在
│   │   ├── __init__.py                          # ✅ 已存在
│   │   ├── fpn_resnet_half.py                   # ✅ 已存在
│   │   ├── fpn_resnet.py                        # ✅ 已存在
│   │   ├── fpn_mask_resnet_half.py              # ✅ 已存在
│   │   ├── fpn_mask_resnet.py                   # ✅ 已存在
│   │   └── pose_dla_dcn.py                      # ✅ 已存在
│   └── heads/                                   # ✅ 已存在
│       ├── __init__.py                          # ✅ 已存在
│       └── lore_tsr_head.py                     # ✅ 已存在
├── my_datasets/table_structure_recognition/     # 🆕 待创建目录
│   ├── lore_tsr_dataset.py                      # 🆕 待创建
│   ├── lore_tsr_transforms.py                   # 🆕 待创建
│   └── lore_tsr_target_preparation.py           # 🆕 待创建
└── modules/utils/lore_tsr/                      # 🆕 待创建目录
    ├── __init__.py                              # 🆕 待创建
    └── dummy_data_utils.py                      # 🆕 待创建
```

## 🔧 具体编码步骤

### 步骤标题
**迭代3步骤3.1: 建立最小训练循环框架**

### 当前迭代
迭代3 - 基础训练循环

### 影响文件
1. `networks/lore_tsr/lore_tsr_loss.py` - 新建基础损失函数
2. `modules/utils/lore_tsr/dummy_data_utils.py` - 新建虚拟数据工具
3. `my_datasets/table_structure_recognition/lore_tsr_dataset.py` - 新建数据集类
4. `my_datasets/table_structure_recognition/lore_tsr_transforms.py` - 新建数据变换
5. `my_datasets/table_structure_recognition/lore_tsr_target_preparation.py` - 新建目标准备（占位）
6. `training_loops/table_structure_recognition/train_lore_tsr.py` - 完善训练循环

### 具体操作

#### 操作1: 创建基础损失函数类
**文件**: `networks/lore_tsr/lore_tsr_loss.py`
**策略**: 复制保留核心算法（简化版本）

```python
#!/usr/bin/env python3
"""
LORE-TSR 基础损失函数实现

迭代3：实现简化版损失函数，包含基本的hm_loss, wh_loss, reg_loss
迭代4：将扩展为完整的损失函数，包含所有6个损失组件

基于原LORE-TSR的losses.py，保持核心算法逻辑不变
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class LoreTsrBasicLoss(nn.Module):
    """LORE-TSR基础损失函数类（简化版本）"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # 损失权重配置
        self.hm_weight = config.loss.weights.get('hm_weight', 1.0)
        self.wh_weight = config.loss.weights.get('wh_weight', 1.0)
        self.off_weight = config.loss.weights.get('off_weight', 1.0)
        
        # 损失函数组件
        self.focal_loss = FocalLoss()
        self.reg_l1_loss = RegL1Loss()
    
    def forward(self, predictions, targets):
        """
        计算LORE-TSR基础损失
        
        Args:
            predictions: 模型预测输出字典
            targets: 目标标签字典
            
        Returns:
            total_loss: 总损失值
            loss_stats: 各项损失统计字典
        """
        # 提取预测和目标
        pred_hm = predictions['hm']
        pred_wh = predictions['wh'] 
        pred_reg = predictions['reg']
        
        gt_hm = targets['hm']
        gt_wh = targets['wh']
        gt_reg = targets['reg']
        gt_reg_mask = targets['reg_mask']
        
        # 计算各项损失
        hm_loss = self.focal_loss(pred_hm, gt_hm)
        wh_loss = self.reg_l1_loss(pred_wh, gt_wh, gt_reg_mask)
        off_loss = self.reg_l1_loss(pred_reg, gt_reg, gt_reg_mask)
        
        # 加权求和
        total_loss = (self.hm_weight * hm_loss + 
                     self.wh_weight * wh_loss + 
                     self.off_weight * off_loss)
        
        # 损失统计
        loss_stats = {
            'total_loss': total_loss.item(),
            'hm_loss': hm_loss.item(),
            'wh_loss': wh_loss.item(),
            'off_loss': off_loss.item()
        }
        
        return total_loss, loss_stats

class FocalLoss(nn.Module):
    """Focal Loss实现（从LORE-TSR复制）"""
    
    def __init__(self, alpha=2, beta=4):
        super().__init__()
        self.alpha = alpha
        self.beta = beta
    
    def forward(self, pred, gt):
        """计算Focal Loss"""
        pos_inds = gt.eq(1).float()
        neg_inds = gt.lt(1).float()
        
        neg_weights = torch.pow(1 - gt, self.beta)
        
        loss = 0
        
        pos_loss = torch.log(pred) * torch.pow(1 - pred, self.alpha) * pos_inds
        neg_loss = torch.log(1 - pred) * torch.pow(pred, self.alpha) * neg_weights * neg_inds
        
        num_pos = pos_inds.float().sum()
        pos_loss = pos_loss.sum()
        neg_loss = neg_loss.sum()
        
        if num_pos == 0:
            loss = loss - neg_loss
        else:
            loss = loss - (pos_loss + neg_loss) / num_pos
        
        return loss

class RegL1Loss(nn.Module):
    """L1回归损失实现（从LORE-TSR复制）"""
    
    def __init__(self):
        super().__init__()
    
    def forward(self, pred, target, mask):
        """计算L1回归损失"""
        expand_mask = mask.unsqueeze(2).expand_as(pred).float()
        loss = F.l1_loss(pred * expand_mask, target * expand_mask, reduction='sum')
        loss = loss / (mask.sum() + 1e-4)
        return loss
```

#### 操作2: 创建虚拟数据工具
**文件**: `modules/utils/lore_tsr/dummy_data_utils.py`
**策略**: 新建工具函数

```python
#!/usr/bin/env python3
"""
LORE-TSR 虚拟数据生成工具

用于迭代3测试训练循环，生成符合train-anything框架TableDataset格式的虚拟数据
支持WTW标注格式和分布式目录组织
"""

import os
import json
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple

def create_dummy_part_structure(base_dir: str, num_parts: int = 1, samples_per_part: int = 10):
    """
    创建符合train-anything数据集组织的虚拟part目录结构
    
    Args:
        base_dir: 基础目录路径
        num_parts: part目录数量
        samples_per_part: 每个part的样本数量
        
    Returns:
        List[str]: 创建的part目录路径列表
    """
    part_dirs = []
    
    for part_idx in range(num_parts):
        part_name = f"part_{part_idx+1:04d}"
        part_dir = os.path.join(base_dir, part_name)
        os.makedirs(part_dir, exist_ok=True)
        
        # 生成虚拟样本
        for sample_idx in range(samples_per_part):
            # 创建虚拟图像文件（空文件）
            image_name = f"image_{sample_idx+1:03d}.jpg"
            image_path = os.path.join(part_dir, image_name)
            with open(image_path, 'w') as f:
                f.write("")  # 空文件占位
            
            # 创建对应的JSON标注
            json_name = f"image_{sample_idx+1:03d}.json"
            json_path = os.path.join(part_dir, json_name)
            
            # 生成WTW格式标注
            annotation = generate_wtw_format_annotation((768, 768))
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(annotation, f, ensure_ascii=False, indent=2)
        
        part_dirs.append(part_dir)
    
    return part_dirs

def generate_wtw_format_annotation(image_size: Tuple[int, int]) -> Dict:
    """
    生成符合WTW格式的单个样本标注数据
    
    Args:
        image_size: 图像尺寸 (width, height)
        
    Returns:
        Dict: WTW格式的标注数据
    """
    width, height = image_size
    
    # 生成虚拟表格结构（3x3表格）
    rows, cols = 3, 3
    cell_width = width // cols
    cell_height = height // rows
    
    cells = []
    
    for row in range(rows):
        for col in range(cols):
            # 计算单元格边界框
            x1 = col * cell_width
            y1 = row * cell_height
            x2 = x1 + cell_width
            y2 = y1 + cell_height
            
            # 添加一些随机偏移
            x1 += random.randint(-5, 5)
            y1 += random.randint(-5, 5)
            x2 += random.randint(-5, 5)
            y2 += random.randint(-5, 5)
            
            # 确保边界在图像范围内
            x1 = max(0, min(x1, width-1))
            y1 = max(0, min(y1, height-1))
            x2 = max(x1+1, min(x2, width))
            y2 = max(y1+1, min(y2, height))
            
            cell = {
                "bbox": {
                    "p1": [x1, y1],
                    "p2": [x2, y1],
                    "p3": [x2, y2],
                    "p4": [x1, y2]
                },
                "lloc": {
                    "start_row": row,
                    "end_row": row,
                    "start_col": col,
                    "end_col": col
                }
            }
            cells.append(cell)
    
    # WTW格式标注
    annotation = {
        "cells": cells,
        "quality": "合格"  # 只加载"合格"样本
    }
    
    return annotation

def create_quality_valid_sample():
    """创建质量合格的样本数据"""
    return generate_wtw_format_annotation((768, 768))
```

### 受影响的现有模块
- 无直接影响，新增模块完全独立

### 复用已有代码
- 继承train-anything框架的TableDataset基类
- 复用accelerate训练循环框架
- 复用OmegaConf配置系统

### 如何验证 (Verification)

#### 验证命令1: 基础损失函数测试
```bash
cd train-anything
python -c "
import torch
import sys
sys.path.append('.')
from networks.lore_tsr.lore_tsr_loss import LoreTsrBasicLoss, FocalLoss, RegL1Loss
from omegaconf import DictConfig

# 创建测试配置
config = DictConfig({
    'loss': {
        'weights': {
            'hm_weight': 1.0,
            'wh_weight': 1.0,
            'off_weight': 1.0
        }
    }
})

# 测试损失函数创建
loss_fn = LoreTsrBasicLoss(config)
print('✅ 基础损失函数创建成功')

# 测试前向传播
batch_size = 2
predictions = {
    'hm': torch.randn(batch_size, 2, 192, 192),
    'wh': torch.randn(batch_size, 8, 192, 192),
    'reg': torch.randn(batch_size, 2, 192, 192)
}

targets = {
    'hm': torch.randn(batch_size, 2, 192, 192),
    'wh': torch.randn(batch_size, 8, 192, 192),
    'reg': torch.randn(batch_size, 2, 192, 192),
    'reg_mask': torch.ones(batch_size, 500)
}

total_loss, loss_stats = loss_fn(predictions, targets)
print(f'✅ 损失计算成功: {total_loss.item():.4f}')
print(f'损失统计: {loss_stats}')
"
```

#### 验证命令2: 虚拟数据工具测试
```bash
cd train-anything
python -c "
import sys
sys.path.append('.')
from modules.utils.lore_tsr.dummy_data_utils import create_dummy_part_structure, generate_wtw_format_annotation
import tempfile
import os

# 测试虚拟数据生成
with tempfile.TemporaryDirectory() as temp_dir:
    part_dirs = create_dummy_part_structure(temp_dir, num_parts=1, samples_per_part=3)
    print(f'✅ 虚拟part目录创建成功: {len(part_dirs)}个')
    
    # 检查文件结构
    part_dir = part_dirs[0]
    files = os.listdir(part_dir)
    print(f'✅ 生成文件数量: {len(files)}个')
    
    # 测试标注格式
    annotation = generate_wtw_format_annotation((768, 768))
    print(f'✅ WTW标注生成成功，单元格数量: {len(annotation[\"cells\"])}')
    print(f'质量标记: {annotation[\"quality\"]}')
"
```

#### 验证命令3: 完整训练循环测试
```bash
cd train-anything
python training_loops/table_structure_recognition/train_lore_tsr.py \
    --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \
    --debug \
    --dry-run
```

**预期输出**: 训练循环能够启动，创建模型，加载虚拟数据，计算损失，完成一个训练步骤

#### 操作3: 创建数据集类框架
**文件**: `my_datasets/table_structure_recognition/lore_tsr_dataset.py`
**策略**: 重构适配框架入口

```python
#!/usr/bin/env python3
"""
LORE-TSR 数据集类实现

继承train-anything框架的TableDataset基类，支持WTW分布式标注格式
迭代3：使用虚拟数据进行训练循环测试
迭代5：将扩展为完整的数据集适配器
"""

import os
import torch
import numpy as np
from typing import Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from my_datasets.table_dataset import TableDataset
from modules.utils.lore_tsr.dummy_data_utils import generate_wtw_format_annotation

class LoreTsrDataset(TableDataset):
    """LORE-TSR数据集类，继承TableDataset基类"""

    def __init__(self, config, mode='train'):
        """
        初始化LORE-TSR数据集

        Args:
            config: 配置对象
            mode: 模式 ('train' 或 'val')
        """
        # 迭代3：使用虚拟数据根目录
        data_root = "/tmp/lore_tsr_dummy_data"

        super().__init__(
            data_root=data_root,
            mode=mode,
            input_h=config.data.input_h,
            input_w=config.data.input_w,
            down_ratio=config.data.down_ratio,
            num_classes=config.model.num_classes
        )

        self.config = config
        self.mode = mode

        # 创建虚拟数据（如果不存在）
        self._setup_dummy_data()

    def _setup_dummy_data(self):
        """设置虚拟数据"""
        from modules.utils.lore_tsr.dummy_data_utils import create_dummy_part_structure

        if not os.path.exists(self.data_root):
            os.makedirs(self.data_root, exist_ok=True)
            create_dummy_part_structure(
                self.data_root,
                num_parts=1,
                samples_per_part=10
            )

    def _prepare_lore_targets(self, annotation: Dict) -> Dict:
        """
        准备LORE-TSR特定的目标格式

        Args:
            annotation: WTW格式标注

        Returns:
            Dict: LORE-TSR目标格式
        """
        # 迭代3：简化版目标准备
        # 迭代5：将实现完整的目标转换逻辑

        batch_size = 1
        output_h, output_w = self.output_h, self.output_w

        # 创建基础目标张量
        targets = {
            'hm': torch.zeros(batch_size, self.num_classes, output_h, output_w),
            'wh': torch.zeros(batch_size, 8, output_h, output_w),
            'reg': torch.zeros(batch_size, 2, output_h, output_w),
            'reg_mask': torch.zeros(batch_size, 500),
            'ind': torch.zeros(batch_size, 500).long(),
        }

        return targets

    def __getitem__(self, index):
        """获取单个样本"""
        # 调用父类获取基础样本
        sample = super().__getitem__(index)

        # 添加LORE-TSR特定的目标准备
        if 'annotation' in sample:
            lore_targets = self._prepare_lore_targets(sample['annotation'])
            sample['targets'].update(lore_targets)

        return sample
```

#### 操作4: 创建数据变换类
**文件**: `my_datasets/table_structure_recognition/lore_tsr_transforms.py`
**策略**: 新建工具函数

```python
#!/usr/bin/env python3
"""
LORE-TSR 数据变换和预处理

实现LORE-TSR数据变换，包括图像归一化、尺寸调整和数据增强
"""

import torch
import torchvision.transforms as transforms
import numpy as np
from typing import Dict, Any

class LoreTsrTransforms:
    """LORE-TSR数据变换类"""

    def __init__(self, config):
        """
        初始化数据变换

        Args:
            config: 配置对象
        """
        self.config = config
        self.input_h = config.data.input_h
        self.input_w = config.data.input_w

        # 图像预处理变换
        self.image_transforms = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((self.input_h, self.input_w)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """
        对输入样本应用完整的数据变换流程

        Args:
            sample: 原始样本数据

        Returns:
            Dict: 变换后的样本数据
        """
        # 图像预处理
        if 'input' in sample:
            # 如果是numpy数组，转换为tensor
            if isinstance(sample['input'], np.ndarray):
                sample['input'] = torch.from_numpy(sample['input'])

            # 应用图像变换
            if sample['input'].dim() == 3:  # [H, W, C]
                sample['input'] = sample['input'].permute(2, 0, 1)  # [C, H, W]

            # 归一化到[0,1]范围
            if sample['input'].max() > 1.0:
                sample['input'] = sample['input'].float() / 255.0

            # 调整尺寸
            sample['input'] = torch.nn.functional.interpolate(
                sample['input'].unsqueeze(0),
                size=(self.input_h, self.input_w),
                mode='bilinear',
                align_corners=False
            ).squeeze(0)

        return sample

def get_lore_tsr_transforms(config, mode='train'):
    """
    获取LORE-TSR数据变换

    Args:
        config: 配置对象
        mode: 模式 ('train' 或 'val')

    Returns:
        LoreTsrTransforms: 数据变换对象
    """
    return LoreTsrTransforms(config)
```

#### 操作5: 创建目标准备占位文件
**文件**: `my_datasets/table_structure_recognition/lore_tsr_target_preparation.py`
**策略**: 新建占位文件

```python
#!/usr/bin/env python3
"""
LORE-TSR 目标准备模块

迭代3：占位实现
迭代5：将实现完整的目标准备逻辑，包括热力图生成、边界框回归目标等
"""

def prepare_lore_tsr_targets(annotation, config):
    """
    准备LORE-TSR训练目标

    迭代3：占位实现
    迭代5：将实现完整逻辑
    """
    # 占位实现
    pass

def create_heatmap_targets(cells, output_size, num_classes):
    """
    创建热力图目标

    迭代5：将实现完整逻辑
    """
    # 占位实现
    pass

def create_bbox_targets(cells, output_size):
    """
    创建边界框回归目标

    迭代5：将实现完整逻辑
    """
    # 占位实现
    pass
```

#### 操作6: 完善训练循环核心逻辑
**文件**: `training_loops/table_structure_recognition/train_lore_tsr.py`
**策略**: 重构适配框架入口

需要修改以下函数：

1. **setup_training_components函数** - 添加损失函数创建
2. **run_training_loop函数** - 实现基础训练循环
3. **prepare_dataloaders函数** - 实现数据加载器创建

具体修改内容：

```python
# 在文件顶部添加导入
from networks.lore_tsr.lore_tsr_loss import LoreTsrBasicLoss
from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
from my_datasets.table_structure_recognition.lore_tsr_transforms import get_lore_tsr_transforms

# 修改setup_training_components函数，添加损失函数创建
def setup_training_components(config, model, optimizer_ckpt, lr_scheduler_ckpt, accelerator):
    """设置训练组件"""
    # ... 现有代码 ...

    # 创建LORE-TSR损失函数
    loss_criterion = LoreTsrBasicLoss(config)
    logger.info("✅ LORE-TSR基础损失函数创建成功")

    # ... 其余代码保持不变 ...

    return loss_criterion, optimizer, lr_scheduler, max_train_steps, train_datasets, train_loaders, val_loaders, seed

# 实现prepare_dataloaders函数
def prepare_dataloaders(config, mode='train'):
    """准备数据加载器"""
    # 创建数据集
    dataset = LoreTsrDataset(config, mode=mode)

    # 创建数据变换
    transforms = get_lore_tsr_transforms(config, mode=mode)
    dataset.transforms = transforms

    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=config.training.batch_size,
        shuffle=(mode == 'train'),
        num_workers=config.data.num_workers,
        pin_memory=True
    )

    return [(f"lore_tsr_{mode}", dataloader)]

# 实现run_training_loop函数的核心逻辑
def run_training_loop(config, model, accelerator, ema_handler, loss_criterion,
                     optimizer, lr_scheduler, weight_dtype, train_loaders, val_loaders,
                     global_step, first_epoch, max_train_steps, best_loss_model_record, best_loss_record_data):
    """执行完整的训练循环"""

    logger.info("开始LORE-TSR训练循环（迭代3：基础实现）")

    for epoch in range(first_epoch, config.training.epochs):
        logger.info(f"训练 Epoch {epoch + 1}/{config.training.epochs}")

        # 训练一个epoch
        model.train()
        epoch_loss = 0.0
        num_batches = 0

        for loader_name, train_loader in train_loaders:
            for batch_idx, batch in enumerate(train_loader):
                # 前向传播
                with accelerator.autocast():
                    predictions = model(batch['input'])
                    total_loss, loss_stats = loss_criterion(predictions, batch['targets'])

                # 反向传播
                accelerator.backward(total_loss)
                optimizer.step()
                optimizer.zero_grad()

                # 更新EMA
                if ema_handler is not None:
                    ema_handler.update(model)

                # 记录损失
                epoch_loss += total_loss.item()
                num_batches += 1
                global_step += 1

                # 记录训练指标
                if global_step % 10 == 0:
                    logger.info(f"Step {global_step}, Loss: {total_loss.item():.4f}")

                # 检查是否达到最大步数
                if global_step >= max_train_steps:
                    break

            if global_step >= max_train_steps:
                break

        # 计算平均损失
        avg_loss = epoch_loss / max(num_batches, 1)
        logger.info(f"Epoch {epoch + 1} 平均损失: {avg_loss:.4f}")

        # 学习率调度
        lr_scheduler.step()

        if global_step >= max_train_steps:
            break

    logger.info(f"训练循环完成，最终步数: {global_step}")
    return global_step
```

### 当前迭代逻辑图
参见上方"动态迁移蓝图"部分的Mermaid图

---

**计划制定时间**: 2025-07-20
**目标迭代**: 迭代3步骤3.1
**预估完成时间**: 2-3小时
**验证要求**: 训练循环能够启动并运行，损失函数正常计算，虚拟数据正常加载
**下一步骤**: 步骤3.2 - 完善数据集适配器和数据变换

## 🚨 重要提醒

1. **严格遵循迁移策略**: 损失函数采用"复制保留"策略，训练循环采用"重构适配"策略
2. **保持项目可运行**: 每个操作完成后都要验证项目能够正常启动
3. **简化版实现**: 当前是迭代3的简化版本，为后续迭代预留扩展接口
4. **验证驱动**: 必须通过所有验证命令才能进入下一步骤
5. **熔断机制**: 如果验证失败，立即停止并记录详细日志

## 📝 执行检查清单

- [ ] 创建`networks/lore_tsr/lore_tsr_loss.py`基础损失函数
- [ ] 创建`modules/utils/lore_tsr/`目录和虚拟数据工具
- [ ] 创建`my_datasets/table_structure_recognition/`目录和相关文件
- [ ] 修改`train_lore_tsr.py`完善训练循环逻辑
- [ ] 执行验证命令1：基础损失函数测试
- [ ] 执行验证命令2：虚拟数据工具测试
- [ ] 执行验证命令3：完整训练循环测试
- [ ] 确认所有验证通过，项目可正常运行
- [ ] 生成步骤3.1执行报告
