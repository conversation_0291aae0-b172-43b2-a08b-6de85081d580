#!/usr/bin/env python3
"""
LORE-TSR Processor组件实现

迁移策略：复制保留 - 从LORE-TSR逐行复制核心算法，确保数值计算完全一致
源文件：LORE-TSR/src/lib/models/classifier.py
目标：train-anything/networks/lore_tsr/processor.py

Time: 2025-07-20
Author: LORE-TSR Migration Team
Description: Processor组件，负责特征提取、位置嵌入和逻辑结构恢复
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import numpy as np
import torch
import torch.nn as nn
from .processor_utils import _tranpose_and_gather_feat, _get_wh_feat, _get_4ps_feat, _normalized_ps
import torch.nn.functional as F

import json
import cv2
import os
from .transformer import Transformer
import math
import time
import random
import time 
import copy

class Stacker(nn.Module):
    def __init__(self, input_size, hidden_size, output_size, layers, heads=8, dropout=0.1):
        super(Stacker, self).__init__()
        self.logi_encoder =  nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(inplace=True) #newly added
        )
        self.tsfm = Transformer(2 * hidden_size, hidden_size, output_size, layers, heads, dropout)

    def forward(self, outputs, logi, mask = None, require_att = False):
      logi_embeddings = self.logi_encoder(logi)

      cat_embeddings = torch.cat((logi_embeddings, outputs), dim=2)

      if mask is None:
        if require_att:
          stacked_axis, att = self.tsfm(cat_embeddings)
        else:
          stacked_axis = self.tsfm(cat_embeddings)
      else:
        stacked_axis = self.tsfm(cat_embeddings, mask=mask)

      if require_att:
        return stacked_axis, att
      else:
        return stacked_axis

class Processor(nn.Module):
    def __init__(self, config):
        super(Processor, self).__init__()
       
        # 从config.processor中获取配置参数
        processor_config = config.processor
        
        if processor_config.wiz_stacking:
          self.stacker = Stacker(processor_config.output_size, processor_config.hidden_size, 
                               processor_config.output_size, processor_config.stacking_layers)

        #input_state, hidden_state, output_state, layers, heads, dropout
        self.tsfm_axis = Transformer(processor_config.input_size, processor_config.hidden_size, 
                                   processor_config.output_size, processor_config.tsfm_layers, 
                                   processor_config.num_heads, processor_config.att_dropout) #original version
        self.x_position_embeddings = nn.Embedding(processor_config.max_fmp_size, processor_config.hidden_size)
        self.y_position_embeddings = nn.Embedding(processor_config.max_fmp_size, processor_config.hidden_size)
        
        self.config = config
        self.processor_config = processor_config
        self.device = torch.device('cpu')  # 默认设备，将在to()方法中更新
    
    def forward(self, outputs, batch = None, cc_match = None, dets = None): #training version forward
      # 'outputs' stands for the feature of cells
      # mask = None
      # att = None

      '''
        Constructing Features:
      '''
      if batch is None:
        # Inference Mode, the four corner features are gathered 
        # during bounding boxes decoding for simplicity (See ctdet_4ps_decode() in ./src/lib/model/decode.py).
        
        vis_feat = outputs
        if dets is None:
          feat = vis_feat

        else:
          left_pe = self.x_position_embeddings(dets[:, :, 0])
          upper_pe = self.y_position_embeddings(dets[:, :, 1])
          right_pe = self.x_position_embeddings(dets[:, :, 2])
          lower_pe = self.y_position_embeddings(dets[:, :, 5])
          feat = vis_feat + left_pe + upper_pe + right_pe + lower_pe

        # !TODO: moving the processings here and uniform the feature construction code for training and inference.
      
      else:
        #Training Mode
        ind = batch['hm_ind']
        mask = batch['hm_mask'] #during training, the attention mask will be applied

        # 处理outputs格式：可能是列表或字典
        if isinstance(outputs, list):
            output = outputs[-1]
        else:
            output = outputs  # 直接使用字典格式

        pred = output['ax']
        ct_feat = _tranpose_and_gather_feat(pred, ind)

        if self.processor_config.wiz_2dpe:        
          cr_feat = _get_4ps_feat(batch['cc_match'], output)
          cr_feat = cr_feat.sum(axis = 3)
          vis_feat = ct_feat + cr_feat
          
          ps = _get_wh_feat(ind, batch, 'gt')
          ps = _normalized_ps(ps, self.processor_config.max_fmp_size)

          left_pe = self.x_position_embeddings(ps[:, :, 0])
          upper_pe = self.y_position_embeddings(ps[:, :, 1])
          right_pe = self.x_position_embeddings(ps[:, :, 2])
          lower_pe = self.y_position_embeddings(ps[:, :, 5])

          feat = vis_feat + left_pe + upper_pe + right_pe + lower_pe

        elif self.processor_config.wiz_4ps:
          cr_feat = _get_4ps_feat(batch['cc_match'], output)
          cr_feat = cr_feat.sum(axis = 3)
          feat = ct_feat + cr_feat

        elif self.processor_config.wiz_vanilla:
          feat = ct_feat

      '''
        Put Features into TSFM:
      '''

      if batch is None:
        #Inference Mode
        logic_axis = self.tsfm_axis(feat) 
        if self.processor_config.wiz_stacking:
            stacked_axis = self.stacker(feat, logic_axis)
      else:
        #Training Mode
        logic_axis = self.tsfm_axis(feat, mask = mask)   
        if self.processor_config.wiz_stacking:
          stacked_axis = self.stacker(feat, logic_axis, mask = mask)

      if self.processor_config.wiz_stacking:
        return logic_axis, stacked_axis
      else:
        return logic_axis 

    def get_logic_axis(self, outputs, batch=None):
        """
        获取逻辑轴向信息
        
        为AxisLoss提供逻辑轴向信息
        
        Args:
            outputs: 模型输出
            batch: 批次数据
            
        Returns:
            logic_axis: 逻辑轴向张量
        """
        result = self.forward(outputs, batch)
        if self.processor_config.wiz_stacking:
            logic_axis, _ = result
            return logic_axis
        else:
            return result
    
    def get_stacked_logic_axis(self, outputs, batch=None):
        """
        获取堆叠逻辑轴向信息
        
        为堆叠AxisLoss提供逻辑轴向信息
        
        Args:
            outputs: 模型输出
            batch: 批次数据
            
        Returns:
            stacked_logic_axis: 堆叠逻辑轴向张量
        """
        if not self.processor_config.wiz_stacking:
            return None
            
        result = self.forward(outputs, batch)
        _, stacked_axis = result
        return stacked_axis
    
    def to(self, device):
        """
        移动到指定设备
        
        Args:
            device: 目标设备
            
        Returns:
            self: 返回自身以支持链式调用
        """
        super().to(device)
        self.device = device
        return self

def load_processor(model, model_path, optimizer=None, resume=False, lr=None, lr_step=None):
    checkpoint = torch.load(model_path, map_location=lambda storage, loc: storage)
    print('loaded {}, epoch {}'.format(model_path, checkpoint['epoch']))
    state_dict = checkpoint['state_dict']
    model.load_state_dict(state_dict)
    return model

def _judge(box):
    countx = len(list(set([box[0],box[2],box[4],box[6]]))) 
    county = len(list(set([box[1],box[3],box[5],box[7]]))) 
    if countx<2 or county<2:
        return False
    
    return True
