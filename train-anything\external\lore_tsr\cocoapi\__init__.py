"""
COCO API 模块
从LORE-TSR原样复制的完整实现，支持COCO格式数据的加载和处理
虽然LORE-TSR使用自定义数据格式，但数据加载模块依赖此API处理COCO格式的JSON文件
"""

# 尝试导入PythonAPI中的核心组件
try:
    import sys
    import os
    
    # 添加PythonAPI到路径
    python_api_path = os.path.join(os.path.dirname(__file__), 'PythonAPI')
    if python_api_path not in sys.path:
        sys.path.insert(0, python_api_path)
    
    # 导入核心COCO类（LORE-TSR主要使用的组件）
    from pycocotools.coco import COCO
    COCO_AVAILABLE = True
    print("✅ COCO API核心功能加载成功")
    __all__ = ['COCO']
    
    # 尝试导入评估功能
    try:
        from pycocotools.cocoeval import COCOeval
        __all__.append('COCOeval')
    except ImportError as e:
        print(f"⚠️  COCOeval导入失败: {e}")
    
    # 尝试导入掩码处理功能
    try:
        from pycocotools import mask
        __all__.append('mask')
    except ImportError as e:
        print(f"⚠️  mask模块导入失败: {e}")
        print("📝 请编译cocoapi: cd external/lore_tsr/cocoapi/PythonAPI && python setup.py build_ext --inplace")

except ImportError as e:
    print(f"❌ COCO API导入失败: {e}")
    print("📝 请检查cocoapi安装: cd external/lore_tsr/cocoapi/PythonAPI && python setup.py build_ext --inplace")
    COCO_AVAILABLE = False
    __all__ = []

# 导出可用性标志
__all__.append('COCO_AVAILABLE')
