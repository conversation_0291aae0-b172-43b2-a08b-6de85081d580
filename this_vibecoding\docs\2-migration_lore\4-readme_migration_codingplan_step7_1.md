# LORE-TSR 迁移编码计划 - 迭代7步骤7.1

## 📋 计划概述

### 步骤标题
**迭代7步骤7.1: DCNv2真实实现完整迁移**

### 当前迭代
**迭代7: 外部依赖集成** (PRD文档第157行定义)

### 迭代目标
完成DCNv2（可变形卷积v2）从LORE-TSR到train-anything的完整迁移，替换现有占位符实现，为后续NMS和cocoapi迁移奠定基础。

## 🗂️ 动态迁移蓝图更新

### 文件迁移映射表 (File Migration Map)

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配 | 迭代1 | **复杂** | ✅ 已完成 |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配 | 迭代1,3 | **复杂** | ✅ 已完成 |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留 | 迭代2 | **复杂** | ✅ 已完成 |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留 | 迭代4 | 简单 | ✅ 已完成 |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留 | 迭代6 | **复杂** | ✅ 已完成 |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留 | 迭代6 | **复杂** | ✅ 已完成 |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留 | 迭代2 | 简单 | ✅ 已完成 |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配 | 迭代5 | **复杂** | ✅ 已完成 |
| `src/lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离 | 迭代7 | 简单 | 🔄 进行中 |
| `src/lib/external/` | `external/lore_tsr/NMS/` | 复制隔离 | 迭代7 | 简单 | 未开始 |
| `cocoapi/` | `external/lore_tsr/cocoapi/` | 复制隔离 | 迭代7 | 简单 | 未开始 |
| `src/lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留 | 迭代11 | 简单 | 未开始 |

### 目标目录结构树 (Target Directory Tree)

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/  # ✅ 已创建
│   └── lore_tsr_config.yaml                      # ✅ 已创建
├── training_loops/table_structure_recognition/   # ✅ 已创建
│   └── train_lore_tsr.py                         # ✅ 已创建
├── networks/lore_tsr/                            # ✅ 已创建
│   ├── __init__.py                               # ✅ 已创建
│   ├── lore_tsr_model.py                         # ✅ 已创建
│   ├── lore_tsr_loss.py                          # ✅ 已创建
│   ├── processor.py                              # ✅ 已创建
│   ├── transformer.py                            # ✅ 已创建
│   ├── backbones/                                # ✅ 已创建
│   │   ├── __init__.py                           # ✅ 已创建
│   │   ├── fpn_resnet_half.py                    # ✅ 已创建
│   │   ├── fpn_resnet.py                         # ✅ 已创建
│   │   ├── fpn_mask_resnet_half.py               # ✅ 已创建
│   │   ├── fpn_mask_resnet.py                    # ✅ 已创建
│   │   └── pose_dla_dcn.py                       # ✅ 已创建
│   └── heads/                                    # ✅ 已创建
│       ├── __init__.py                           # ✅ 已创建
│       └── lore_tsr_head.py                      # ✅ 已创建
├── my_datasets/table_structure_recognition/      # ✅ 已创建
│   ├── lore_tsr_dataset.py                       # ✅ 已创建
│   ├── lore_tsr_transforms.py                    # ✅ 已创建
│   └── lore_tsr_target_preparation.py            # ✅ 已创建
├── modules/utils/lore_tsr/                       # [待创建]
│   ├── __init__.py                               # [待创建]
│   ├── post_process.py                           # [待创建]
│   ├── oracle_utils.py                           # [待创建]
│   └── eval_utils.py                             # [待创建]
├── modules/visualization/                        # [待创建]
│   └── lore_tsr_visualizer.py                    # [待创建]
└── external/lore_tsr/                            # ✅ 已创建
    ├── __init__.py                               # ✅ 已创建
    ├── DCNv2/                                    # 🔄 步骤7.1更新中
    │   ├── __init__.py                           # 🔄 步骤7.1更新中
    │   ├── dcn_v2.py                             # 🔄 步骤7.1替换中
    │   ├── dcn_v2_alt.py                         # [步骤7.1新增]
    │   ├── dcn_v2_onnx.py                        # [步骤7.1新增]
    │   ├── setup.py                              # [步骤7.1新增]
    │   ├── LICENSE                               # [步骤7.1新增]
    │   ├── README.md                             # [步骤7.1新增]
    │   ├── install.sh                            # [步骤7.1新增]
    │   ├── install_cuda_fix.sh                   # [步骤7.1新增]
    │   ├── install_once.sh                       # [步骤7.1新增]
    │   ├── make.sh                               # [步骤7.1新增]
    │   ├── direct_build.sh                       # [步骤7.1新增]
    │   ├── set_env.sh                            # [步骤7.1新增]
    │   ├── testcpu.py                            # [步骤7.1新增]
    │   ├── testcuda.py                           # [步骤7.1新增]
    │   └── src/                                  # [步骤7.1新增]
    │       ├── dcn_v2.h                          # [步骤7.1新增]
    │       ├── vision.cpp                        # [步骤7.1新增]
    │       ├── cpu/                              # [步骤7.1新增]
    │       │   ├── dcn_v2_cpu.cpp               # [步骤7.1新增]
    │       │   └── dcn_v2_im2col_cpu.cpp        # [步骤7.1新增]
    │       └── cuda/                             # [步骤7.1新增]
    │           ├── dcn_v2_cuda.cu               # [步骤7.1新增]
    │           ├── dcn_v2_im2col_cuda.cu        # [步骤7.1新增]
    │           └── dcn_v2_cuda_kernel.cu        # [步骤7.1新增]
    ├── NMS/                                      # [步骤7.2待创建]
    └── cocoapi/                                  # [步骤7.3待创建]
```

## 🎯 步骤7.1具体操作

### 影响文件
- `external/lore_tsr/DCNv2/` (整个目录内容替换)
- `external/lore_tsr/DCNv2/__init__.py` (更新导入逻辑)

### 具体操作步骤

#### 1. 清理现有占位符实现
```bash
# 清理当前占位符实现（保留目录结构）
rm -f external/lore_tsr/DCNv2/dcn_v2.py
rm -f external/lore_tsr/DCNv2/__init__.py
```

#### 2. 从LORE-TSR完整复制DCNv2目录
```bash
# 完整复制LORE-TSR的DCNv2实现
cp -r LORE-TSR/src/lib/models/networks/DCNv2/* external/lore_tsr/DCNv2/
```

#### 3. 更新DCNv2模块初始化文件
创建新的 `external/lore_tsr/DCNv2/__init__.py`，使用LORE-TSR原有的容错机制：

```python
"""
DCNv2 (Deformable Convolutional Networks v2) 模块
从LORE-TSR原样复制的完整实现，包含完善的容错机制
"""

# 直接导入LORE-TSR的DCNv2实现，它已经包含了完善的容错机制：
# 1. 尝试导入预编译的_ext模块
# 2. 回退到torchvision.ops.deform_conv2d
# 3. 提供清晰的错误信息和警告
from .dcn_v2 import DCN, DCNv2

# 尝试导入其他可能的组件
try:
    from .dcn_v2 import DCNPooling
    __all__ = ['DCN', 'DCNv2', 'DCNPooling']
except ImportError:
    __all__ = ['DCN', 'DCNv2']
```

#### 4. 更新外部依赖主模块
更新 `external/lore_tsr/__init__.py`：

```python
"""
LORE-TSR外部依赖模块
包含DCNv2、NMS、cocoapi的完整实现
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "LORE-TSR Team"

# 迭代7.1：DCNv2真实实现已集成
# DCNv2自带完善的容错机制，无需额外处理
from .DCNv2 import DCN, DCNv2

__all__ = [
    "__version__",
    "DCN",
    "DCNv2",
]
```

### 受影响的现有模块
- `networks/lore_tsr/backbones/pose_dla_dcn.py` - 将使用真实DCNv2实现
- 所有使用DCN的模型组件 - 性能将得到提升

### 复用已有代码
- 保持现有的导入路径不变
- 维持向后兼容性
- 复用train-anything的错误处理机制

## 🔍 验证方案 (Verification)

### 验证命令1: 基础导入测试
```bash
python -c "
from external.lore_tsr.DCNv2 import DCN, DCNv2;
print('✅ DCNv2组件导入成功');
import torch;
dcn = DCN(64, 64);
print(f'✅ DCN实例创建成功: {type(dcn)}');
x = torch.randn(1, 64, 32, 32);
y = dcn(x);
print(f'✅ DCN前向传播成功: {y.shape}');
print('🎉 步骤7.1基础验证通过')
"
```

### 验证命令2: 模型集成测试
```bash
python -c "
from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model;
from omegaconf import OmegaConf;
config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml');
model = create_lore_tsr_model(config);
print('✅ 模型创建成功（使用真实DCNv2）');
import torch;
x = torch.randn(1, 3, 768, 768);
with torch.no_grad(): y = model(x);
print(f'✅ 模型前向传播成功: {list(y[0].keys()) if isinstance(y, list) else list(y.keys())}');
print('🎉 步骤7.1模型集成验证通过')
"
```

### 验证命令3: 训练循环兼容性测试
```bash
python -c "
import sys; sys.path.append('training_loops/table_structure_recognition');
from train_lore_tsr import main;
print('✅ 训练入口导入成功');
print('✅ DCNv2集成不影响训练循环');
print('🎉 步骤7.1训练兼容性验证通过')
"
```

## 📊 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代7步骤7.1 - DCNv2真实实现迁移

    subgraph "Source: LORE-TSR/src/lib/models/networks/DCNv2/"
        direction LR
        S1["dcn_v2.py - 主要实现"]
        S2["setup.py - 编译脚本"]
        S3["src/ - C++/CUDA源码"]
        S4["install.sh - 安装脚本"]
        S5["LICENSE - 许可证"]
    end

    subgraph "Target: train-anything/external/lore_tsr/DCNv2/"
        direction LR
        T1["dcn_v2.py (替换占位符)"]
        T2["setup.py (新增)"]
        T3["src/ (新增)"]
        T4["install.sh (新增)"]
        T5["LICENSE (新增)"]
        T6["__init__.py (更新)"]
    end

    subgraph "Built-in Fallback: torchvision.ops"
        direction LR
        F1["deform_conv2d (PyTorch内置)"]
        F2["自动容错机制"]
    end

    %% 迁移映射
    S1 -- "Copy & Replace" --> T1
    S2 -- "Copy" --> T2
    S3 -- "Copy" --> T3
    S4 -- "Copy" --> T4
    S5 -- "Copy" --> T5

    %% 内置容错机制
    T1 -.-> F1
    T6 --> T1

    %% 依赖关系
    T6 --> T1

    %% 使用关系
    T1 -.-> pose_dla_dcn["networks/lore_tsr/backbones/pose_dla_dcn.py"]
```

## 📋 执行检查清单

- [ ] 清理现有占位符实现文件
- [ ] 从LORE-TSR完整复制DCNv2目录内容
- [ ] 更新 `external/lore_tsr/DCNv2/__init__.py` 使用原生容错机制
- [ ] 更新 `external/lore_tsr/__init__.py` 导出DCN组件
- [ ] 执行验证命令1：基础导入测试
- [ ] 执行验证命令2：模型集成测试
- [ ] 执行验证命令3：训练循环兼容性测试
- [ ] 确认项目保持完全可运行状态
- [ ] 记录编译说明文档

## 🚨 风险控制

### 技术风险
1. **DCNv2编译失败** - LORE-TSR原实现已包含torchvision回退机制
2. **导入路径冲突** - 保持现有导入路径不变
3. **性能提升** - 真实实现性能将优于占位符

### 内置容错机制
LORE-TSR的DCNv2实现已包含完善的容错机制：
1. 优先使用预编译的_ext模块（最高性能）
2. 自动回退到torchvision.ops.deform_conv2d（兼容性保证）
3. 提供清晰的警告信息和状态反馈

---

**文档版本**: v1.0  
**创建日期**: 2025-07-20  
**当前迭代**: 迭代7步骤7.1  
**预估工期**: 0.5个工作日  
**风险等级**: 低（有完整回退机制）  
**下一步骤**: 步骤7.2 - NMS模块迁移

**⚠️ 重要提醒**: 本计划仅制定步骤7.1，执行完成后需等待用户确认再制定步骤7.2。
