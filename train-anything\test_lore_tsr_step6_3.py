#!/usr/bin/env python3
"""
LORE-TSR 迁移验证测试 - 步骤6.3：训练循环深度集成和AxisLoss完善

验证内容：
1. 训练循环集成验证
2. AxisLoss集成验证
3. 端到端训练流程验证
4. 堆叠模式验证
5. 项目完整性验证

Time: 2025-07-20
Author: LORE-TSR Migration Team
"""

import sys
import torch
import traceback
from omegaconf import OmegaConf

def test_training_loop_integration():
    """测试训练循环集成"""
    print("=" * 60)
    print("测试1: 训练循环集成验证")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        from networks.lore_tsr.processor import Processor
        from omegaconf import OmegaConf
        import torch
        
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        model = create_lore_tsr_model(config)
        processor = Processor(config)
        
        print("✅ 训练组件创建成功")
        
        # 创建符合LORE-TSR格式的真实测试数据
        dummy_predictions = {
            'hm': torch.randn(2, 2, 192, 192) * 0.1,  # 小的logits
            'wh': torch.randn(2, 8, 192, 192) * 0.1,
            'reg': torch.randn(2, 2, 192, 192) * 0.1,
            'ax': torch.randn(2, 256, 192, 192) * 0.1
        }
        # GT hm应该是0-1之间的热图
        gt_hm = torch.zeros(2, 2, 192, 192)
        gt_hm[:, 0, 96, 96] = 1.0  # 在中心位置设置一个热点
        dummy_targets = {
            'hm': gt_hm,
            'hm_mask': torch.ones(2, 100),
            'hm_ind': torch.randint(0, 36864, (2, 100)),
            'logic': torch.randn(2, 100, 4) * 0.1
        }
        
        # 测试Processor调用
        if config.processor.wiz_stacking:
            logic_axis, stacked_axis = processor(dummy_predictions, dummy_targets)
            print(f"✅ 堆叠模式Processor调用成功: logic_axis{logic_axis.shape}, stacked_axis{stacked_axis.shape}")
        else:
            logic_axis = processor(dummy_predictions, dummy_targets)
            print(f"✅ 基础模式Processor调用成功: logic_axis{logic_axis.shape}")
        
        return True
    except Exception as e:
        print(f"❌ 训练循环集成验证失败: {e}")
        traceback.print_exc()
        return False

def test_axis_loss_integration():
    """测试AxisLoss集成"""
    print("\n" + "=" * 60)
    print("测试2: AxisLoss集成验证")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
        from networks.lore_tsr.processor import Processor
        from omegaconf import OmegaConf
        import torch
        
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        loss_fn = LoreTsrLoss(config)
        processor = Processor(config)
        
        # 创建符合LORE-TSR格式的真实测试数据
        dummy_predictions = {
            'hm': torch.randn(2, 2, 192, 192) * 0.1,  # 小的logits
            'wh': torch.randn(2, 8, 192, 192) * 0.1,
            'reg': torch.randn(2, 2, 192, 192) * 0.1,
            'ax': torch.randn(2, 256, 192, 192) * 0.1
        }
        # GT hm应该是0-1之间的热图
        gt_hm = torch.zeros(2, 2, 192, 192)
        gt_hm[:, 0, 96, 96] = 1.0  # 在中心位置设置一个热点
        dummy_targets = {
            'hm': gt_hm,
            'hm_mask': torch.ones(2, 100),
            'hm_ind': torch.randint(0, 36864, (2, 100)),
            'logic': torch.randn(2, 100, 4) * 0.1
        }
        
        # 获取真实logic_axis
        logic_axis = processor(dummy_predictions, dummy_targets)
        if config.processor.wiz_stacking:
            logic_axis, stacked_axis = logic_axis
        
        # 测试损失计算
        total_loss, loss_stats = loss_fn(dummy_predictions, dummy_targets, logic_axis)
        
        print(f"✅ AxisLoss计算成功: 总损失{total_loss.item():.4f}")
        print(f"✅ 损失统计: {list(loss_stats.keys())}")
        print(f"  - hm_loss: {loss_stats.get('hm_loss', 0):.4f}")
        print(f"  - wh_loss: {loss_stats.get('wh_loss', 0):.4f}")
        print(f"  - off_loss: {loss_stats.get('off_loss', 0):.4f}")
        print(f"  - ax_loss: {loss_stats.get('ax_loss', 0):.4f}")
        
        # 测试无logic_axis的回退模式
        total_loss_fallback, loss_stats_fallback = loss_fn(dummy_predictions, dummy_targets, None)
        print(f"✅ 回退模式损失计算成功: 总损失{total_loss_fallback.item():.4f}")
        
        return True
    except Exception as e:
        print(f"❌ AxisLoss集成验证失败: {e}")
        traceback.print_exc()
        return False

def test_end_to_end_training():
    """测试端到端训练流程"""
    print("\n" + "=" * 60)
    print("测试3: 端到端训练流程验证")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
        from networks.lore_tsr.processor import Processor
        from omegaconf import OmegaConf
        import torch
        
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        
        # 创建所有组件
        model = create_lore_tsr_model(config)
        loss_fn = LoreTsrLoss(config)
        processor = Processor(config)
        
        print("✅ 所有组件创建成功")
        
        # 模拟完整的训练步骤
        dummy_input = torch.randn(2, 3, 768, 768)  # 模拟输入图像
        # GT hm应该是0-1之间的热图
        gt_hm = torch.zeros(2, 2, 192, 192)
        gt_hm[:, 0, 96, 96] = 1.0  # 在中心位置设置一个热点
        dummy_targets = {
            'hm': gt_hm,
            'wh': torch.randn(2, 100, 8) * 0.1,  # 正确的wh目标维度：[batch, max_objects, 8]
            'reg': torch.randn(2, 100, 2) * 0.1,  # 正确的reg目标维度：[batch, max_objects, 2]
            'hm_mask': torch.ones(2, 100),
            'hm_ind': torch.randint(0, 36864, (2, 100)),
            'logic': torch.randn(2, 100, 4) * 0.1
        }
        
        # 前向传播
        predictions = model(dummy_input)
        if isinstance(predictions, list) and len(predictions) > 0:
            predictions = predictions[0]
        
        print(f"✅ 模型前向传播成功: 输出键{list(predictions.keys())}")
        
        # Processor调用
        if config.processor.wiz_stacking:
            logic_axis, stacked_axis = processor(predictions, dummy_targets)
            print(f"✅ Processor调用成功（堆叠模式）: logic_axis{logic_axis.shape}, stacked_axis{stacked_axis.shape}")
        else:
            logic_axis = processor(predictions, dummy_targets)
            print(f"✅ Processor调用成功（基础模式）: logic_axis{logic_axis.shape}")
        
        # 损失计算
        total_loss, loss_stats = loss_fn(predictions, dummy_targets, logic_axis)
        print(f"✅ 损失计算成功: 总损失{total_loss.item():.4f}")
        
        # 反向传播测试
        total_loss.backward()
        print("✅ 反向传播成功")
        
        return True
    except Exception as e:
        print(f"❌ 端到端训练流程验证失败: {e}")
        traceback.print_exc()
        return False

def test_stacking_mode():
    """测试堆叠模式"""
    print("\n" + "=" * 60)
    print("测试4: 堆叠模式验证")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.processor import Processor
        from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
        from omegaconf import OmegaConf
        import torch
        
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        
        # 临时启用堆叠模式进行测试
        original_stacking = config.processor.wiz_stacking
        config.processor.wiz_stacking = True
        
        processor = Processor(config)
        loss_fn = LoreTsrLoss(config)
        
        print(f"✅ 堆叠模式组件创建成功 (wiz_stacking={config.processor.wiz_stacking})")
        
        # 创建符合LORE-TSR格式的真实测试数据
        dummy_predictions = {
            'hm': torch.randn(2, 2, 192, 192) * 0.1,  # 小的logits
            'wh': torch.randn(2, 8, 192, 192) * 0.1,
            'reg': torch.randn(2, 2, 192, 192) * 0.1,
            'ax': torch.randn(2, 256, 192, 192) * 0.1
        }
        # GT hm应该是0-1之间的热图
        gt_hm = torch.zeros(2, 2, 192, 192)
        gt_hm[:, 0, 96, 96] = 1.0  # 在中心位置设置一个热点
        dummy_targets = {
            'hm': gt_hm,
            'hm_mask': torch.ones(2, 100),
            'hm_ind': torch.randint(0, 36864, (2, 100)),
            'logic': torch.randn(2, 100, 4) * 0.1
        }
        
        # 测试堆叠模式的Processor
        logic_axis, stacked_axis = processor(dummy_predictions, dummy_targets)
        print(f"✅ 堆叠模式Processor: logic_axis{logic_axis.shape}, stacked_axis{stacked_axis.shape}")
        
        # 测试堆叠模式的损失计算
        total_loss, loss_stats = loss_fn(dummy_predictions, dummy_targets, logic_axis)
        print(f"✅ 堆叠模式损失计算: 总损失{total_loss.item():.4f}")
        
        if 'sax_loss' in loss_stats:
            print(f"  - sax_loss: {loss_stats['sax_loss']:.4f}")
        
        # 恢复原始配置
        config.processor.wiz_stacking = original_stacking
        
        return True
    except Exception as e:
        print(f"❌ 堆叠模式验证失败: {e}")
        traceback.print_exc()
        return False

def test_project_integrity():
    """测试项目完整性"""
    print("\n" + "=" * 60)
    print("测试5: 项目完整性验证")
    print("=" * 60)
    
    try:
        # 测试所有组件仍然可用
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
        from networks.lore_tsr.transformer import Transformer
        from networks.lore_tsr.processor import Processor
        from omegaconf import OmegaConf
        
        print("✅ 所有组件导入成功")
        
        # 验证配置文件兼容性
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        print("✅ 配置文件加载成功")
        
        # 验证所有组件可以正常实例化
        model = create_lore_tsr_model(config)
        loss_fn = LoreTsrLoss(config)
        processor = Processor(config)
        transformer = Transformer(256, 256, 4, 6, 8, 0.1)
        
        print("✅ 所有组件实例化成功")
        
        # 验证迭代6的完整功能
        print("✅ 迭代6完整功能验证:")
        print("  - ✅ Transformer组件（步骤6.1）")
        print("  - ✅ Processor组件（步骤6.2）")
        print("  - ✅ 训练循环深度集成（步骤6.3）")
        print("  - ✅ AxisLoss完善（步骤6.3）")
        
        print("✅ 项目保持完全可运行状态")
        
        return True
    except Exception as e:
        print(f"❌ 项目完整性验证失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始LORE-TSR步骤6.3验证测试")
    print("测试目标：训练循环深度集成和AxisLoss完善")
    
    test_results = []
    
    # 执行所有测试
    test_results.append(("训练循环集成验证", test_training_loop_integration()))
    test_results.append(("AxisLoss集成验证", test_axis_loss_integration()))
    test_results.append(("端到端训练流程验证", test_end_to_end_training()))
    test_results.append(("堆叠模式验证", test_stacking_mode()))
    test_results.append(("项目完整性验证", test_project_integrity()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 步骤6.3验证测试全部通过！")
        print("✅ 训练循环成功集成Processor调用")
        print("✅ AxisLoss使用真实logic_axis计算损失")
        print("✅ 支持wiz_stacking模式的堆叠逻辑轴向")
        print("✅ 项目整体保持可运行状态")
        print("🎊 迭代6完整功能验证通过！")
        return True
    else:
        print("❌ 部分测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
