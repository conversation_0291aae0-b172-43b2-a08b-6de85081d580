# LORE-TSR 迁移编码计划 - 迭代7步骤7.2

## 📋 计划概述

### 步骤标题
**迭代7步骤7.2: NMS模块完整迁移**

### 当前迭代
**迭代7: 外部依赖集成** (PRD文档第157行定义)

### 迭代目标
完成NMS（非极大值抑制）模块从LORE-TSR到train-anything的完整迁移，包括Cython实现的高性能NMS算法和基于Shapely的几何NMS实现，为后续cocoapi迁移奠定基础。

## 🗂️ 动态迁移蓝图更新

### 文件迁移映射表 (File Migration Map)

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配 | 迭代1 | **复杂** | ✅ 已完成 |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配 | 迭代1,3 | **复杂** | ✅ 已完成 |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留 | 迭代2 | **复杂** | ✅ 已完成 |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留 | 迭代4 | 简单 | ✅ 已完成 |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留 | 迭代6 | **复杂** | ✅ 已完成 |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留 | 迭代6 | **复杂** | ✅ 已完成 |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留 | 迭代2 | 简单 | ✅ 已完成 |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配 | 迭代5 | **复杂** | ✅ 已完成 |
| `src/lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离 | 迭代7 | 简单 | ✅ 已完成 |
| `src/lib/external/` | `external/lore_tsr/NMS/` | 复制隔离 | 迭代7 | 简单 | 🔄 进行中 |
| `cocoapi/` | `external/lore_tsr/cocoapi/` | 复制隔离 | 迭代7 | 简单 | 未开始 |
| `src/lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留 | 迭代11 | 简单 | 未开始 |

### 目标目录结构树 (Target Directory Tree)

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/  # ✅ 已创建
│   └── lore_tsr_config.yaml                      # ✅ 已创建
├── training_loops/table_structure_recognition/   # ✅ 已创建
│   └── train_lore_tsr.py                         # ✅ 已创建
├── networks/lore_tsr/                            # ✅ 已创建
│   ├── __init__.py                               # ✅ 已创建
│   ├── lore_tsr_model.py                         # ✅ 已创建
│   ├── lore_tsr_loss.py                          # ✅ 已创建
│   ├── processor.py                              # ✅ 已创建
│   ├── transformer.py                            # ✅ 已创建
│   ├── backbones/                                # ✅ 已创建
│   │   ├── __init__.py                           # ✅ 已创建
│   │   ├── fpn_resnet_half.py                    # ✅ 已创建
│   │   ├── fpn_resnet.py                         # ✅ 已创建
│   │   ├── fpn_mask_resnet_half.py               # ✅ 已创建
│   │   ├── fpn_mask_resnet.py                    # ✅ 已创建
│   │   └── pose_dla_dcn.py                       # ✅ 已创建
│   └── heads/                                    # ✅ 已创建
│       ├── __init__.py                           # ✅ 已创建
│       └── lore_tsr_head.py                      # ✅ 已创建
├── my_datasets/table_structure_recognition/      # ✅ 已创建
│   ├── lore_tsr_dataset.py                       # ✅ 已创建
│   ├── lore_tsr_transforms.py                    # ✅ 已创建
│   └── lore_tsr_target_preparation.py            # ✅ 已创建
├── modules/utils/lore_tsr/                       # [待创建]
│   ├── __init__.py                               # [待创建]
│   ├── post_process.py                           # [待创建]
│   ├── oracle_utils.py                           # [待创建]
│   └── eval_utils.py                             # [待创建]
├── modules/visualization/                        # [待创建]
│   └── lore_tsr_visualizer.py                    # [待创建]
└── external/lore_tsr/                            # ✅ 已创建
    ├── __init__.py                               # ✅ 已创建
    ├── DCNv2/                                    # ✅ 已完成
    │   ├── __init__.py                           # ✅ 已完成
    │   ├── dcn_v2.py                             # ✅ 已完成
    │   ├── dcn_v2_alt.py                         # ✅ 已完成
    │   ├── dcn_v2_onnx.py                        # ✅ 已完成
    │   ├── setup.py                              # ✅ 已完成
    │   ├── LICENSE                               # ✅ 已完成
    │   ├── README.md                             # ✅ 已完成
    │   ├── install.sh                            # ✅ 已完成
    │   ├── install_cuda_fix.sh                   # ✅ 已完成
    │   ├── install_once.sh                       # ✅ 已完成
    │   ├── make.sh                               # ✅ 已完成
    │   ├── direct_build.sh                       # ✅ 已完成
    │   ├── set_env.sh                            # ✅ 已完成
    │   ├── testcpu.py                            # ✅ 已完成
    │   ├── testcuda.py                           # ✅ 已完成
    │   └── src/                                  # ✅ 已完成
    │       ├── dcn_v2.h                          # ✅ 已完成
    │       ├── vision.cpp                        # ✅ 已完成
    │       ├── cpu/                              # ✅ 已完成
    │       │   ├── dcn_v2_cpu.cpp               # ✅ 已完成
    │       │   └── dcn_v2_im2col_cpu.cpp        # ✅ 已完成
    │       └── cuda/                             # ✅ 已完成
    │           ├── dcn_v2_cuda.cu               # ✅ 已完成
    │           ├── dcn_v2_im2col_cuda.cu        # ✅ 已完成
    │           └── dcn_v2_cuda_kernel.cu        # ✅ 已完成
    ├── NMS/                                      # 🔄 步骤7.2创建中
    │   ├── __init__.py                           # [步骤7.2新增]
    │   ├── nms.pyx                               # [步骤7.2新增]
    │   ├── setup.py                              # [步骤7.2新增]
    │   ├── Makefile                              # [步骤7.2新增]
    │   └── shapelyNMS.py                         # [步骤7.2新增]
    └── cocoapi/                                  # [步骤7.3待创建]
```

## 🎯 步骤7.2具体操作

### 影响文件
- `external/lore_tsr/NMS/` (新建目录及所有内容)
- `external/lore_tsr/__init__.py` (更新导入逻辑)

### 具体操作步骤

#### 1. 创建NMS目录结构
```bash
# 创建NMS目录
mkdir -p external/lore_tsr/NMS
```

#### 2. 从LORE-TSR完整复制NMS文件
```bash
# 完整复制LORE-TSR的NMS实现
cp LORE-TSR/src/lib/external/nms.pyx external/lore_tsr/NMS/
cp LORE-TSR/src/lib/external/setup.py external/lore_tsr/NMS/
cp LORE-TSR/src/lib/external/Makefile external/lore_tsr/NMS/
cp LORE-TSR/src/lib/external/shapelyNMS.py external/lore_tsr/NMS/
```

#### 3. 创建NMS模块初始化文件
创建 `external/lore_tsr/NMS/__init__.py`，支持Cython编译和回退机制：

```python
"""
NMS (Non-Maximum Suppression) 模块
从LORE-TSR原样复制的完整实现，包含Cython高性能实现和Shapely几何回退
"""

# 尝试导入编译后的Cython NMS实现
try:
    from .nms import nms, soft_nms, soft_nms_39, soft_nms_merge
    NMS_CYTHON_AVAILABLE = True
    print("✅ NMS Cython实现加载成功")
    __all__ = ['nms', 'soft_nms', 'soft_nms_39', 'soft_nms_merge']
except ImportError as e:
    print(f"⚠️  NMS Cython实现导入失败: {e}")
    print("📝 请编译NMS: cd external/lore_tsr/NMS && python setup.py build_ext --inplace")
    NMS_CYTHON_AVAILABLE = False
    __all__ = []

# 导入Shapely几何NMS实现作为备选
try:
    from .shapelyNMS import delet_min_first, delet_min
    SHAPELY_NMS_AVAILABLE = True
    __all__.extend(['delet_min_first', 'delet_min'])
except ImportError as e:
    print(f"⚠️  Shapely NMS实现导入失败: {e}")
    SHAPELY_NMS_AVAILABLE = False

# 导出可用性标志
__all__.extend(['NMS_CYTHON_AVAILABLE', 'SHAPELY_NMS_AVAILABLE'])
```

#### 4. 更新外部依赖主模块
更新 `external/lore_tsr/__init__.py`：

```python
"""
LORE-TSR外部依赖模块
包含DCNv2、NMS、cocoapi的完整实现
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "LORE-TSR Team"

# 迭代7.1：DCNv2真实实现已集成
from .DCNv2 import DCN, DCNv2

# 迭代7.2：NMS模块已集成
from .NMS import NMS_CYTHON_AVAILABLE, SHAPELY_NMS_AVAILABLE

__all__ = [
    "__version__",
    "DCN",
    "DCNv2",
    "NMS_CYTHON_AVAILABLE",
    "SHAPELY_NMS_AVAILABLE",
]

# 条件导入NMS函数
if NMS_CYTHON_AVAILABLE:
    from .NMS import nms, soft_nms, soft_nms_39, soft_nms_merge
    __all__.extend(['nms', 'soft_nms', 'soft_nms_39', 'soft_nms_merge'])

if SHAPELY_NMS_AVAILABLE:
    from .NMS import delet_min_first, delet_min
    __all__.extend(['delet_min_first', 'delet_min'])
```

### 受影响的现有模块
- 无直接影响，NMS模块为独立的外部依赖
- 为后续可能的后处理功能提供支持

### 复用已有代码
- 保持LORE-TSR原有的NMS实现不变
- 复用train-anything的模块化结构
- 采用与DCNv2相同的容错机制模式

## 🔍 验证方案 (Verification)

### 验证命令1: 基础导入测试
```bash
python -c "
from external.lore_tsr.NMS import NMS_CYTHON_AVAILABLE, SHAPELY_NMS_AVAILABLE;
print(f'✅ NMS模块导入成功');
print(f'  - Cython NMS可用: {NMS_CYTHON_AVAILABLE}');
print(f'  - Shapely NMS可用: {SHAPELY_NMS_AVAILABLE}');
if SHAPELY_NMS_AVAILABLE:
    from external.lore_tsr.NMS import delet_min_first, delet_min;
    print('✅ Shapely NMS函数导入成功');
print('🎉 步骤7.2基础验证通过')
"
```

### 验证命令2: NMS功能测试
```bash
python -c "
import numpy as np;
from external.lore_tsr.NMS import SHAPELY_NMS_AVAILABLE;
if SHAPELY_NMS_AVAILABLE:
    from external.lore_tsr.NMS import delet_min_first;
    # 创建测试数据
    dets = np.array([[10, 10, 50, 50, 0.9], [15, 15, 55, 55, 0.8]]);
    pts = [[[10,10],[50,10],[50,50],[10,50]], [[15,15],[55,15],[55,55],[15,55]]];
    areas = [1600, 1600];
    inter_areas = [[0, 400], [400, 0]];
    min_areas = [[1600, 1600], [1600, 1600]];
    scores = [0.9, 0.8];
    result = delet_min_first(dets, pts, areas, inter_areas, min_areas, scores, 0.5, 0.1);
    print(f'✅ Shapely NMS功能测试成功: {result.shape}');
else:
    print('⚠️  Shapely NMS不可用，跳过功能测试');
print('🎉 步骤7.2功能验证通过')
"
```

### 验证命令3: 外部依赖集成测试
```bash
python -c "
from external.lore_tsr import DCN, DCNv2, NMS_CYTHON_AVAILABLE, SHAPELY_NMS_AVAILABLE;
print('✅ 外部依赖模块集成测试成功');
print(f'  - DCN可用: True');
print(f'  - DCNv2可用: True');
print(f'  - NMS Cython可用: {NMS_CYTHON_AVAILABLE}');
print(f'  - NMS Shapely可用: {SHAPELY_NMS_AVAILABLE}');
print('✅ 迭代7步骤7.2不影响现有功能');
print('🎉 步骤7.2集成验证通过')
"
```

## 📊 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代7步骤7.2 - NMS模块迁移

    subgraph "Source: LORE-TSR/src/lib/external/"
        direction LR
        S1["nms.pyx - Cython高性能实现"]
        S2["setup.py - 编译脚本"]
        S3["Makefile - Make编译文件"]
        S4["shapelyNMS.py - Shapely几何实现"]
        S5["__init__.py - 空文件"]
    end

    subgraph "Target: train-anything/external/lore_tsr/NMS/"
        direction LR
        T1["nms.pyx (复制)"]
        T2["setup.py (复制)"]
        T3["Makefile (复制)"]
        T4["shapelyNMS.py (复制)"]
        T5["__init__.py (新建)"]
    end

    subgraph "Fallback: Shapely几何NMS"
        direction LR
        F1["delet_min_first (几何NMS)"]
        F2["delet_min (几何NMS)"]
    end

    subgraph "High Performance: Cython NMS"
        direction LR
        H1["nms (标准NMS)"]
        H2["soft_nms (软NMS)"]
        H3["soft_nms_39 (软NMS变体)"]
        H4["soft_nms_merge (合并软NMS)"]
    end

    %% 迁移映射
    S1 -- "Copy" --> T1
    S2 -- "Copy" --> T2
    S3 -- "Copy" --> T3
    S4 -- "Copy" --> T4
    S5 -- "Replace with Smart Init" --> T5

    %% 容错机制
    T1 -.-> H1
    T1 -.-> H2
    T1 -.-> H3
    T1 -.-> H4
    T4 -.-> F1
    T4 -.-> F2

    %% 依赖关系
    T5 --> T1
    T5 --> T4
    T5 -.-> F1
    T5 -.-> H1
```

## 📋 执行检查清单

- [ ] 创建 `external/lore_tsr/NMS/` 目录
- [ ] 从LORE-TSR复制 `nms.pyx` 文件
- [ ] 从LORE-TSR复制 `setup.py` 文件
- [ ] 从LORE-TSR复制 `Makefile` 文件
- [ ] 从LORE-TSR复制 `shapelyNMS.py` 文件
- [ ] 创建智能的 `external/lore_tsr/NMS/__init__.py`
- [ ] 更新 `external/lore_tsr/__init__.py` 导出NMS组件
- [ ] 执行验证命令1：基础导入测试
- [ ] 执行验证命令2：NMS功能测试
- [ ] 执行验证命令3：外部依赖集成测试
- [ ] 确认项目保持完全可运行状态
- [ ] 记录编译说明文档

## 🚨 风险控制

### 技术风险
1. **NMS Cython编译失败** - 提供Shapely几何NMS作为回退实现
2. **Shapely依赖缺失** - 在__init__.py中提供清晰的错误信息
3. **导入路径冲突** - 保持独立的NMS目录结构

### 内置容错机制
1. **双重实现**: Cython高性能实现 + Shapely几何回退
2. **智能导入**: 根据可用性动态导入相应的NMS函数
3. **清晰反馈**: 提供详细的可用性状态和编译指导

### 性能考虑
1. **优先级**: Cython实现 > Shapely实现
2. **兼容性**: 两种实现都保持原有的函数接口
3. **可扩展性**: 为后续可能的其他NMS实现预留接口

---

**文档版本**: v1.0
**创建日期**: 2025-07-20
**当前迭代**: 迭代7步骤7.2
**预估工期**: 0.5个工作日
**风险等级**: 低（有完整回退机制）
**下一步骤**: 步骤7.3 - cocoapi模块迁移

**⚠️ 重要提醒**: 本计划仅制定步骤7.2，执行完成后需等待用户确认再制定步骤7.3。
