#!/bin/bash
# 设置PyTorch库路径
export TORCH_LIB_PATH=$(python -c "import torch; import os; print(os.path.join(os.path.dirname(torch.__file__), 'lib'))")
export LD_LIBRARY_PATH=$TORCH_LIB_PATH:$LD_LIBRARY_PATH

# 设置CUDA路径
if [ -d "/usr/local/cuda" ]; then
    export CUDA_HOME="/usr/local/cuda"
    export PATH=$CUDA_HOME/bin:$PATH
    export LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH
fi

echo "Environment set up for DCNv2 with CUDA support."
echo "PyTorch library path: $TORCH_LIB_PATH"
echo "CUDA_HOME: $CUDA_HOME"
