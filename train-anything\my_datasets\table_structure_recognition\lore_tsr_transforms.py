#!/usr/bin/env python3
"""
LORE-TSR 数据变换和预处理

实现LORE-TSR数据变换，包括图像归一化、尺寸调整和数据增强
"""

import torch
import torchvision.transforms as transforms
import numpy as np
from typing import Dict, Any


class LoreTsrTransforms:
    """LORE-TSR数据变换类"""

    def __init__(self, config):
        """
        初始化数据变换

        Args:
            config: 配置对象
        """
        self.config = config
        self.input_h = config.data.processing.image_size[0]
        self.input_w = config.data.processing.image_size[1]

        # 图像预处理变换
        self.image_transforms = transforms.Compose([
            transforms.ToPILImage(),
            transforms.Resize((self.input_h, self.input_w)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """
        对输入样本应用完整的数据变换流程

        Args:
            sample: 原始样本数据

        Returns:
            Dict: 变换后的样本数据
        """
        try:
            # 图像预处理
            if 'input' in sample:
                input_data = sample['input']

                # 处理不同的输入格式
                if isinstance(input_data, np.ndarray):
                    input_data = torch.from_numpy(input_data)
                elif isinstance(input_data, str):
                    # 如果是文件路径，创建虚拟数据
                    input_data = torch.randn(3, self.input_h, self.input_w)

                # 确保数据类型和形状正确
                if input_data.dim() == 3 and input_data.shape[0] != 3:
                    input_data = input_data.permute(2, 0, 1)  # [H, W, C] -> [C, H, W]

                # 归一化到[0,1]范围
                if input_data.max() > 1.0:
                    input_data = input_data.float() / 255.0

                # 调整尺寸
                if input_data.shape[-2:] != (self.input_h, self.input_w):
                    input_data = torch.nn.functional.interpolate(
                        input_data.unsqueeze(0),
                        size=(self.input_h, self.input_w),
                        mode='bilinear',
                        align_corners=False
                    ).squeeze(0)

                sample['input'] = input_data

            # 验证数据完整性
            self._validate_sample(sample)

        except Exception as e:
            # 错误处理：创建默认样本
            print(f"数据变换失败，使用默认样本: {e}")
            sample = self._create_default_sample()

        return sample

    def _validate_sample(self, sample: Dict[str, Any]):
        """验证样本数据完整性"""
        if 'input' not in sample:
            raise ValueError("样本缺少input字段")

        input_shape = sample['input'].shape
        if len(input_shape) != 3 or input_shape[0] != 3:
            raise ValueError(f"输入形状错误: {input_shape}")

        if input_shape[1:] != (self.input_h, self.input_w):
            raise ValueError(f"输入尺寸错误: {input_shape[1:]}")

    def _create_default_sample(self) -> Dict[str, Any]:
        """创建默认样本"""
        return {
            'input': torch.randn(3, self.input_h, self.input_w),
            'targets': {},
            'metadata': {'is_default': True}
        }


def get_lore_tsr_transforms(config, mode='train'):
    """
    获取LORE-TSR数据变换

    Args:
        config: 配置对象
        mode: 模式 ('train' 或 'val')

    Returns:
        LoreTsrTransforms: 数据变换对象
    """
    return LoreTsrTransforms(config)
