#!/usr/bin/env python3
"""
LORE-TSR 权重格式转换器

迭代8步骤8.1：权重转换器框架
负责将LORE-TSR的分离权重文件转换为train-anything的统一格式
"""

import os
import torch
import logging
from typing import Dict, Optional, List, Union, Tuple
from pathlib import Path

from .weight_utils import (
    remove_module_prefix,
    detect_checkpoint_format,
    create_weight_mapping_rules,
    validate_weight_file,
    get_weight_file_info,
    apply_weight_key_mapping,
    get_checkpoint_save_path
)

logger = logging.getLogger(__name__)


class LoreTsrWeightConverter:
    """
    LORE-TSR权重格式转换器
    
    负责将LORE-TSR的分离权重文件（model.pth和processor.pth）
    转换为train-anything的统一格式（pytorch_model.bin）
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化转换器配置
        
        Args:
            config (dict, optional): 转换器配置
        """
        self.config = config or self._get_default_config()
        self.key_mappings = create_weight_mapping_rules()
        self.ignore_keys = self.config.get('ignore_keys', [])
        self.strict_mode = self.config.get('strict_mode', False)
        
        logger.info(f"权重转换器初始化完成，严格模式: {self.strict_mode}")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'strict_mode': False,
            'ignore_keys': [
                'optimizer',
                'lr_scheduler', 
                'epoch',
                'best_result',
                'args'
            ],
            'validate_conversion': True,
            'save_conversion_log': True
        }
    
    def convert_lore_weights(self, model_path: str, processor_path: str, 
                           output_path: str) -> bool:
        """
        执行完整的LORE-TSR权重转换流程
        
        Args:
            model_path (str): LORE-TSR模型权重文件路径
            processor_path (str): LORE-TSR处理器权重文件路径
            output_path (str): 转换后权重保存路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            logger.info(f"开始权重转换流程")
            logger.info(f"  模型权重: {model_path}")
            logger.info(f"  处理器权重: {processor_path}")
            logger.info(f"  输出路径: {output_path}")
            
            # 步骤1：验证输入文件
            if not self._validate_input_files(model_path, processor_path):
                return False
            
            # 步骤2：加载LORE权重
            model_weights = self.load_lore_model_weights(model_path)
            if model_weights is None:
                return False
                
            processor_weights = self.load_lore_processor_weights(processor_path)
            if processor_weights is None:
                return False
            
            # 步骤3：合并和映射权重
            unified_weights = self.merge_and_map_weights(model_weights, processor_weights)
            if unified_weights is None:
                return False
            
            # 步骤4：保存转换后的权重
            success = self.save_converted_weights(unified_weights, output_path)
            
            if success:
                logger.info(f"权重转换成功完成: {output_path}")
                if self.config.get('save_conversion_log', True):
                    self._save_conversion_log(model_path, processor_path, output_path, unified_weights)
            else:
                logger.error("权重转换失败")
            
            return success
            
        except Exception as e:
            logger.error(f"权重转换过程中出错: {e}")
            return False
    
    def load_lore_model_weights(self, model_path: str) -> Optional[Dict]:
        """
        加载LORE-TSR模型权重
        
        Args:
            model_path (str): 模型权重文件路径
            
        Returns:
            dict or None: 模型权重字典
        """
        try:
            logger.info(f"加载LORE模型权重: {model_path}")
            
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # 提取状态字典
            if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
                logger.info(f"从checkpoint中提取state_dict，包含 {len(state_dict)} 个参数")
            else:
                state_dict = checkpoint
                logger.info(f"直接使用权重字典，包含 {len(state_dict)} 个参数")
            
            # 记录一些样本键名
            sample_keys = list(state_dict.keys())[:5]
            logger.debug(f"模型权重样本键名: {sample_keys}")
            
            return state_dict
            
        except Exception as e:
            logger.error(f"加载LORE模型权重失败: {e}")
            return None
    
    def load_lore_processor_weights(self, processor_path: str) -> Optional[Dict]:
        """
        加载LORE-TSR处理器权重
        
        Args:
            processor_path (str): 处理器权重文件路径
            
        Returns:
            dict or None: 处理器权重字典
        """
        try:
            logger.info(f"加载LORE处理器权重: {processor_path}")
            
            checkpoint = torch.load(processor_path, map_location='cpu')
            
            # 提取状态字典
            if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
                logger.info(f"从checkpoint中提取state_dict，包含 {len(state_dict)} 个参数")
            else:
                state_dict = checkpoint
                logger.info(f"直接使用权重字典，包含 {len(state_dict)} 个参数")
            
            # 记录一些样本键名
            sample_keys = list(state_dict.keys())[:5]
            logger.debug(f"处理器权重样本键名: {sample_keys}")
            
            return state_dict
            
        except Exception as e:
            logger.error(f"加载LORE处理器权重失败: {e}")
            return None
    
    def merge_and_map_weights(self, model_weights: Dict, processor_weights: Dict) -> Optional[Dict]:
        """
        合并权重并应用键名映射
        
        Args:
            model_weights (dict): 模型权重字典
            processor_weights (dict): 处理器权重字典
            
        Returns:
            dict or None: 合并后的统一权重字典
        """
        try:
            logger.info("开始合并和映射权重")
            
            unified_weights = {}
            
            # 处理模型权重
            logger.info(f"处理模型权重，共 {len(model_weights)} 个参数")
            for key, value in model_weights.items():
                if self._should_ignore_key(key):
                    logger.debug(f"忽略键: {key}")
                    continue
                
                mapped_key = apply_weight_key_mapping(key, self.key_mappings, 'model')
                
                if mapped_key in unified_weights:
                    logger.warning(f"键名冲突: {mapped_key} (原键: {key})")
                    if self.strict_mode:
                        raise ValueError(f"严格模式下不允许键名冲突: {mapped_key}")
                
                unified_weights[mapped_key] = value
            
            # 处理处理器权重
            logger.info(f"处理处理器权重，共 {len(processor_weights)} 个参数")
            for key, value in processor_weights.items():
                if self._should_ignore_key(key):
                    logger.debug(f"忽略键: {key}")
                    continue
                
                mapped_key = apply_weight_key_mapping(key, self.key_mappings, 'processor')
                
                if mapped_key in unified_weights:
                    logger.warning(f"键名冲突: {mapped_key} (原键: {key})")
                    if self.strict_mode:
                        raise ValueError(f"严格模式下不允许键名冲突: {mapped_key}")
                
                unified_weights[mapped_key] = value
            
            logger.info(f"权重合并完成，统一权重包含 {len(unified_weights)} 个参数")
            
            # 记录一些样本键名
            sample_keys = list(unified_weights.keys())[:10]
            logger.debug(f"统一权重样本键名: {sample_keys}")
            
            return unified_weights
            
        except Exception as e:
            logger.error(f"合并和映射权重失败: {e}")
            return None
    
    def save_converted_weights(self, state_dict: Dict, output_path: str) -> bool:
        """
        保存转换后的权重
        
        Args:
            state_dict (dict): 转换后的权重字典
            output_path (str): 保存路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            logger.info(f"保存转换后的权重到: {output_path}")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存权重
            torch.save(state_dict, output_path)
            
            # 验证保存的文件
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
                logger.info(f"权重保存成功，文件大小: {file_size:.2f} MB")
                return True
            else:
                logger.error("权重保存失败，文件不存在")
                return False
                
        except Exception as e:
            logger.error(f"保存权重失败: {e}")
            return False
    
    def _validate_input_files(self, model_path: str, processor_path: str) -> bool:
        """验证输入文件"""
        if not validate_weight_file(model_path):
            logger.error(f"模型权重文件无效: {model_path}")
            return False
        
        if not validate_weight_file(processor_path):
            logger.error(f"处理器权重文件无效: {processor_path}")
            return False
        
        return True
    
    def _should_ignore_key(self, key: str) -> bool:
        """检查是否应该忽略某个键"""
        for ignore_pattern in self.ignore_keys:
            if ignore_pattern in key:
                return True
        return False
    
    def _save_conversion_log(self, model_path: str, processor_path: str, 
                           output_path: str, unified_weights: Dict):
        """保存转换日志"""
        try:
            log_path = output_path.replace('.bin', '_conversion_log.txt')
            
            with open(log_path, 'w', encoding='utf-8') as f:
                f.write("LORE-TSR 权重转换日志\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"模型权重路径: {model_path}\n")
                f.write(f"处理器权重路径: {processor_path}\n")
                f.write(f"输出路径: {output_path}\n")
                f.write(f"转换后参数数量: {len(unified_weights)}\n\n")
                
                f.write("转换后权重键名列表:\n")
                f.write("-" * 30 + "\n")
                for key in sorted(unified_weights.keys()):
                    f.write(f"{key}\n")
            
            logger.info(f"转换日志已保存: {log_path}")
            
        except Exception as e:
            logger.warning(f"保存转换日志失败: {e}")
