#!/usr/bin/env python3
"""测试模型输出格式"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
from omegaconf import OmegaConf
import torch

# 加载配置
config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')

# 创建模型
model = create_lore_tsr_model(config)

# 测试输入
input_tensor = torch.randn(1, 3, 768, 768)

# 前向传播
with torch.no_grad():
    outputs = model(input_tensor)

print(f'模型输出类型: {type(outputs)}')
print(f'模型输出长度: {len(outputs)}')

if isinstance(outputs, list):
    print('模型输出是列表格式:')
    for i, output in enumerate(outputs):
        if hasattr(output, 'shape'):
            print(f'  输出[{i}]形状: {output.shape}')
        else:
            print(f'  输出[{i}]类型: {type(output)}')
else:
    print(f'模型输出键: {list(outputs.keys())}')
