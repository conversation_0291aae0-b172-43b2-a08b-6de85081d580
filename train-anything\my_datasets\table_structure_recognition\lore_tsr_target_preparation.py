#!/usr/bin/env python3
"""
LORE-TSR 目标准备模块

迭代3步骤3.2：实现基础目标准备逻辑
迭代5：将扩展为完整的目标准备逻辑
"""

import torch
import numpy as np
import math
from typing import Dict, List, Tuple, Any


def prepare_lore_tsr_targets(annotation: Dict, config) -> Dict:
    """
    准备LORE-TSR训练目标

    迭代3步骤3.2：基础实现
    迭代5：完整实现

    Args:
        annotation: WTW格式标注
        config: 配置对象

    Returns:
        Dict: LORE-TSR目标格式
    """
    input_h = config.data.processing.image_size[0]
    input_w = config.data.processing.image_size[1]
    output_h = input_h // config.data.processing.down_ratio
    output_w = input_w // config.data.processing.down_ratio
    num_classes = config.model.heads.hm
    max_objs = 500  # 默认最大目标数

    # 提取单元格信息
    cells = annotation.get('cells', [])

    # 创建目标张量
    targets = {
        'hm': torch.zeros(num_classes, output_h, output_w),
        'wh': torch.zeros(max_objs, 8),
        'reg': torch.zeros(max_objs, 2),
        'reg_mask': torch.zeros(max_objs),
        'ind': torch.zeros(max_objs).long(),
        'num_objs': len(cells)
    }

    # 处理每个单元格
    for idx, cell in enumerate(cells[:max_objs]):
        # 提取边界框坐标
        bbox = cell.get('bbox', {})
        if not bbox:
            continue

        # 获取四个角点
        p1 = bbox.get('p1', [0, 0])
        p2 = bbox.get('p2', [0, 0])
        p3 = bbox.get('p3', [0, 0])
        p4 = bbox.get('p4', [0, 0])

        # 计算中心点
        center_x = (p1[0] + p2[0] + p3[0] + p4[0]) / 4.0
        center_y = (p1[1] + p2[1] + p3[1] + p4[1]) / 4.0

        # 缩放到输出尺寸
        center_x = center_x * output_w / input_w
        center_y = center_y * output_h / input_h

        # 确保在边界内
        center_x = max(0, min(center_x, output_w - 1))
        center_y = max(0, min(center_y, output_h - 1))

        # 计算整数坐标和偏移
        ct_int_x, ct_int_y = int(center_x), int(center_y)
        ct_offset_x, ct_offset_y = center_x - ct_int_x, center_y - ct_int_y

        # 设置热力图目标（类别0为单元格）
        targets['hm'][0, ct_int_y, ct_int_x] = 1.0

        # 设置回归目标
        targets['reg'][idx, 0] = ct_offset_x
        targets['reg'][idx, 1] = ct_offset_y
        targets['reg_mask'][idx] = 1.0

        # 设置索引
        targets['ind'][idx] = ct_int_y * output_w + ct_int_x

        # 设置边界框目标（8个坐标值）
        # 缩放四个角点到输出尺寸
        scaled_points = []
        for point in [p1, p2, p3, p4]:
            scaled_x = point[0] * output_w / input_w
            scaled_y = point[1] * output_h / input_h
            scaled_points.extend([scaled_x - center_x, scaled_y - center_y])

        targets['wh'][idx] = torch.tensor(scaled_points[:8])

    return targets


def create_heatmap_targets(cells: List[Dict], output_size: Tuple[int, int], num_classes: int) -> torch.Tensor:
    """
    创建热力图目标

    Args:
        cells: 单元格列表
        output_size: 输出尺寸 (height, width)
        num_classes: 类别数量

    Returns:
        torch.Tensor: 热力图目标 [num_classes, H, W]
    """
    output_h, output_w = output_size
    heatmap = torch.zeros(num_classes, output_h, output_w)

    for cell in cells:
        bbox = cell.get('bbox', {})
        if not bbox:
            continue

        # 计算中心点
        points = [bbox.get(f'p{i}', [0, 0]) for i in range(1, 5)]
        center_x = sum(p[0] for p in points) / 4.0
        center_y = sum(p[1] for p in points) / 4.0

        # 转换到输出坐标系
        ct_x = int(center_x * output_w / 768)  # 假设输入尺寸768
        ct_y = int(center_y * output_h / 768)

        # 确保在边界内
        if 0 <= ct_x < output_w and 0 <= ct_y < output_h:
            heatmap[0, ct_y, ct_x] = 1.0  # 类别0为单元格

    return heatmap


def create_bbox_targets(cells: List[Dict], output_size: Tuple[int, int]) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    创建边界框回归目标

    Args:
        cells: 单元格列表
        output_size: 输出尺寸 (height, width)

    Returns:
        Tuple[torch.Tensor, torch.Tensor]: (边界框目标, 掩码)
    """
    max_objs = 500
    bbox_targets = torch.zeros(max_objs, 8)
    bbox_mask = torch.zeros(max_objs)

    for idx, cell in enumerate(cells[:max_objs]):
        bbox = cell.get('bbox', {})
        if not bbox:
            continue

        # 提取四个角点并展平
        points = []
        for i in range(1, 5):
            point = bbox.get(f'p{i}', [0, 0])
            points.extend(point)

        bbox_targets[idx] = torch.tensor(points[:8])
        bbox_mask[idx] = 1.0

    return bbox_targets, bbox_mask
