---
trigger: manual
---

由于时刻交接需要，请你生成一份结构清晰、信息完整、格式为 Markdown 的交接文档，必须包含以下内容，并满足以下要求：

---

## 一、工作背景与上下文

- 用 bullet points 的方式，提取当前任务的背景、目标、已知约束。
- 用 Q&A 的方式，提炼你在任务中遇到的问题和解决方法，便于快速恢复上下文。
- 判断哪些上下文信息可以丢弃，哪些必须保留（避免 token 爆炸）。

## 二、已完成工作内容

- 已完成的任务说明（尽量详细）
- 对应的文件、模块或路径，建议格式如下：
  - `src/module/foo.py`: 实现了 xx 功能
  - `docs/design.md`: 含有系统设计说明，建议重点阅读第 3 节

## 三、未完成工作事项

- 列出所有未完成的任务，使用分点列表
- 对每一项，说明当前进展、建议的完成步骤、优先级
- 指明预计涉及的代码层级、模块名称或文件路径

## 四、存在的灰色区域或可优化点

- 哪些代码虽然实现了功能，但存在设计不优、耦合过高、潜在 bug？
- 如果有时间，建议做哪些改进？

## 五、方法与工具

- 你当前采用了哪些方法或框架？背后的原理或理由是？
- 有哪些注意事项或常见坑？

## 六、推荐的下一步工作计划

- 建议优先做什么？为什么？
- 哪些模块风险高，建议优先重构？
- 有无后续扩展建议或需要注意的性能问题？

## 七、适用场景说明

- 本文档适用于哪类任务场景？
- 哪些情况下不适用或需要补充更多上下文？

## 八、自查总结（可嵌套）

- 你有没有遗漏依赖？（请自查）
- 是否与原始需求不一致？（请回溯并标注）
- 若缺乏设计文档，请主动询问用户或提醒接手人补全

## 九、附录：可视化图表

- 请生成至少两个图示，使用 Mermaid 语法：
  - 时序图（sequenceDiagram）：描述模块/接口交互流程
  - 逻辑结构图（flowchart / erDiagram）：描述系统组成或数据关系

---

以上内容请输出到 @vibe_coding/3_timeline/handover-xxx.md 路径下，文件名附上日期时间等（如 handover-v202506171050.md），形成工作交接的markdown文档。
p.s.: 请使用幽默但专业的风格书写，语言不要啰嗦但要覆盖完整。