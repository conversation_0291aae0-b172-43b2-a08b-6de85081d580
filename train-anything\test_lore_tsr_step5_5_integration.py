#!/usr/bin/env python3
"""
LORE-TSR 迁移项目 - 步骤5.5集成验证测试脚本

迭代5步骤5.5：配置系统集成和端到端验证
测试完整的配置系统集成和端到端功能验证

验证内容：
1. 配置系统完善验证
2. 数据集与模型接口验证
3. 损失函数集成验证
4. 端到端训练验证
5. 完整系统稳定性验证
"""

import sys
import os
import torch
import numpy as np
import traceback
from pathlib import Path
from omegaconf import OmegaConf

# 添加train-anything到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_config_system():
    """测试配置系统完善"""
    print("=" * 60)
    print("测试1: 配置系统完善验证")
    print("=" * 60)
    
    try:
        # 加载配置文件
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        print('✅ 配置文件加载成功')
        
        # 验证主要配置节
        main_sections = ['basic', 'data', 'model', 'training']
        for section in main_sections:
            assert section in config, f"缺少配置节: {section}"
        print(f'✅ 主要配置节验证通过: {main_sections}')
        
        # 验证数据配置
        data_sections = ['paths', 'processing', 'targets', 'augmentation']
        for section in data_sections:
            assert section in config.data, f"缺少数据配置节: {section}"
        print(f'✅ 数据配置节验证通过: {data_sections}')
        
        # 验证关键参数
        assert config.data.processing.image_size == [768, 768], f"图像尺寸错误: {config.data.processing.image_size}"
        assert config.data.processing.down_ratio == 4, f"下采样比例错误: {config.data.processing.down_ratio}"
        assert config.data.targets.max_cors == 1200, f"最大角点数错误: {config.data.targets.max_cors}"
        assert config.data.targets.max_pairs == 900, f"最大配对数错误: {config.data.targets.max_pairs}"
        print('✅ 关键参数验证通过')
        
        print("✅ 配置系统完善验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置系统验证失败: {e}")
        traceback.print_exc()
        return False

def test_dataset_model_interface():
    """测试数据集与模型接口验证"""
    print("\n" + "=" * 60)
    print("测试2: 数据集与模型接口验证")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        
        # 加载配置
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        
        # 创建数据集
        dataset = LoreTsrDataset(config, mode='train')
        print(f'✅ 数据集创建成功: {len(dataset)} 个样本')
        
        # 创建模型
        model = create_lore_tsr_model(config)
        print('✅ 模型创建成功')
        print(f'   模型参数数量: {sum(p.numel() for p in model.parameters())}')
        
        # 测试数据加载和模型前向
        if len(dataset) > 0:
            sample = dataset[0]
            input_tensor = sample['input'].unsqueeze(0)  # 添加batch维度
            print(f'✅ 数据加载成功')
            print(f'   输入张量形状: {input_tensor.shape}')
            print(f'   样本键: {list(sample.keys())}')
            
            # 模型前向传播
            model.eval()
            with torch.no_grad():
                outputs = model(input_tensor)
            print(f'✅ 模型前向传播成功')

            # 处理模型输出格式（LORE-TSR模型返回list[dict]）
            if isinstance(outputs, list) and len(outputs) > 0:
                outputs = outputs[0]  # 取第一个输出字典

            print(f'   模型输出键: {list(outputs.keys())}')

            # 验证输出形状
            expected_keys = ['hm', 'wh', 'reg']
            for key in expected_keys:
                if key in outputs:
                    print(f'   {key}输出形状: {outputs[key].shape}')
            
            print("✅ 数据集与模型接口验证通过")
            return True
        else:
            print("⚠️  数据集为空，跳过模型验证")
            return True
        
    except Exception as e:
        print(f"❌ 数据集与模型接口验证失败: {e}")
        traceback.print_exc()
        return False

def test_loss_function_integration():
    """测试损失函数集成验证"""
    print("\n" + "=" * 60)
    print("测试3: 损失函数集成验证")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
        
        # 加载配置
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        
        # 创建组件
        dataset = LoreTsrDataset(config, mode='train')
        model = create_lore_tsr_model(config)
        loss_fn = LoreTsrLoss(config)
        
        print('✅ 所有组件创建成功')
        
        # 测试完整流程
        if len(dataset) > 0:
            sample = dataset[0]
            input_tensor = sample['input'].unsqueeze(0)
            
            # 准备目标张量
            targets = {}
            target_keys = ['hm', 'wh', 'reg', 'hm_mask']
            for key in target_keys:
                if key in sample:
                    targets[key] = sample[key].unsqueeze(0)
            
            print(f'✅ 目标张量准备完成: {list(targets.keys())}')
            
            # 前向传播
            model.eval()
            with torch.no_grad():
                outputs = model(input_tensor)

            # 处理模型输出格式（LORE-TSR模型返回list[dict]）
            if isinstance(outputs, list) and len(outputs) > 0:
                outputs = outputs[0]  # 取第一个输出字典

            print(f'✅ 模型前向传播完成')

            # 损失计算
            loss_result = loss_fn(outputs, targets)

            # 处理损失函数返回格式（返回tuple: (total_loss, loss_stats)）
            if isinstance(loss_result, tuple):
                total_loss, loss_stats = loss_result
            else:
                total_loss = loss_result
                loss_stats = {}

            print(f'✅ 损失计算完成')
            print(f'   总损失: {total_loss.item():.4f}')
            print(f'   损失项: {list(loss_stats.keys()) if loss_stats else "无详细损失项"}')
            
            print("✅ 损失函数集成验证通过")
            return True
        else:
            print("⚠️  数据集为空，跳过损失验证")
            return True
        
    except Exception as e:
        print(f"❌ 损失函数集成验证失败: {e}")
        traceback.print_exc()
        return False

def test_end_to_end_training():
    """测试端到端训练验证"""
    print("\n" + "=" * 60)
    print("测试4: 端到端训练验证")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
        import torch.optim as optim
        from torch.utils.data import DataLoader
        
        # 加载配置
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        
        # 创建组件
        dataset = LoreTsrDataset(config, mode='train')
        model = create_lore_tsr_model(config)
        loss_fn = LoreTsrLoss(config)
        optimizer = optim.Adam(model.parameters(), lr=1e-4)
        
        print('✅ 训练组件创建成功')
        
        # 创建数据加载器
        if len(dataset) > 0:
            dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
            print(f'✅ 数据加载器创建成功: {len(dataloader)} 个批次')
            
            # 测试训练循环
            model.train()
            total_loss = 0
            num_batches = min(2, len(dataloader))  # 测试2个批次
            
            for i, batch in enumerate(dataloader):
                if i >= num_batches:
                    break
                
                optimizer.zero_grad()
                
                # 前向传播
                outputs = model(batch['input'])

                # 处理模型输出格式（LORE-TSR模型返回list[dict]）
                if isinstance(outputs, list) and len(outputs) > 0:
                    outputs = outputs[0]  # 取第一个输出字典

                # 准备目标
                targets = {}
                target_keys = ['hm', 'wh', 'reg', 'hm_mask']
                for key in target_keys:
                    if key in batch:
                        targets[key] = batch[key]

                # 损失计算
                loss_result = loss_fn(outputs, targets)

                # 处理损失函数返回格式（返回tuple: (total_loss, loss_stats)）
                if isinstance(loss_result, tuple):
                    loss, loss_stats = loss_result
                else:
                    loss = loss_result
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
                print(f'✅ 批次 {i+1}/{num_batches}, 损失: {loss.item():.4f}')
            
            avg_loss = total_loss / num_batches
            print(f'✅ 平均损失: {avg_loss:.4f}')
            print("✅ 端到端训练验证通过")
            return True
        else:
            print("⚠️  数据集为空，跳过训练验证")
            return True
        
    except Exception as e:
        print(f"❌ 端到端训练验证失败: {e}")
        traceback.print_exc()
        return False

def test_system_stability():
    """测试系统稳定性验证"""
    print("\n" + "=" * 60)
    print("测试5: 系统稳定性验证")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
        
        # 加载配置
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        
        # 验证所有组件可以正常创建
        dataset = LoreTsrDataset(config, mode='train')
        model = create_lore_tsr_model(config)
        loss_fn = LoreTsrLoss(config)
        
        print('✅ 迭代5所有组件验证通过')
        print(f'✅ 数据集: {len(dataset)} 个样本')
        print(f'✅ 模型: {sum(p.numel() for p in model.parameters())} 个参数')
        print('✅ 损失函数: 所有损失项正常')
        print('🎉 迭代5完整验证成功，准备开始迭代6')
        
        return True
        
    except Exception as e:
        print(f"❌ 系统稳定性验证失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("LORE-TSR 迁移项目 - 步骤5.5集成验证测试")
    print("测试目标: 验证配置系统集成和端到端功能")
    print("迁移策略: 重构适配框架入口")
    
    # 执行所有测试
    tests = [
        test_config_system,
        test_dataset_model_interface,
        test_loss_function_integration,
        test_end_to_end_training,
        test_system_stability
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            results.append(False)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！步骤5.5验证成功")
        print("✅ 配置系统集成完成")
        print("✅ 端到端验证通过")
        print("✅ 迭代5完整验证成功")
        return True
    else:
        print("❌ 部分测试失败，需要检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
