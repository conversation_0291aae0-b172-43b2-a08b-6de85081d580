# LORE-TSR 迁移项目迭代3详细设计文档

## 📋 项目概述

### 迭代3目标
实现基于accelerate的最小训练循环框架，为LORE-TSR在train-anything框架中建立完整的训练基础设施。本迭代专注于训练循环的核心逻辑，使用简化的损失函数和基础数据加载，为后续迭代奠定坚实基础。

### 设计原则
- **简约至上**：实现满足当前需求的最简单训练循环
- **框架集成**：深度集成accelerate分布式训练和OmegaConf配置系统
- **迭代演进**：为后续损失函数和数据集适配预留清晰接口
- **参考最佳实践**：严格遵循cycle-centernet-ms的成功模式

## 目录结构树 (Directory Tree)

```
train-anything/
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                    # [修改] 完善训练循环实现
├── networks/lore_tsr/
│   ├── lore_tsr_model.py                    # [已存在] 模型工厂
│   ├── lore_tsr_loss.py                     # [新增] 基础损失函数
│   └── heads/lore_tsr_head.py               # [已存在] 检测头
├── my_datasets/table_structure_recognition/
│   ├── lore_tsr_dataset.py                  # [新增] 继承TableDataset的LORE-TSR数据集
│   ├── lore_tsr_transforms.py               # [新增] 数据变换
│   └── lore_tsr_target_preparation.py       # [新增] 目标准备（占位）
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                # [已存在] 配置文件
└── modules/utils/lore_tsr/
    └── dummy_data_utils.py                  # [新增] 虚拟数据工具（基于TableDataset格式）
```

## 整体逻辑和交互时序图

```mermaid
sequenceDiagram
    participant Main as main()
    participant Config as parse_args()
    participant Env as prepare_training_enviornment_v2()
    participant Model as create_model_and_ema()
    participant Data as prepare_dataloaders()
    participant TrainLoop as run_training_loop()
    participant Loss as LoreTsrBasicLoss
    participant Save as save_state()

    Main->>Config: 解析配置和命令行参数
    Config-->>Main: config对象
    Main->>Env: 初始化accelerator环境
    Env-->>Main: accelerator, weight_dtype
    Main->>Model: 创建LORE-TSR模型和EMA
    Model-->>Main: model, ema_handler
    Main->>Data: 准备训练和验证数据加载器
    Data-->>Main: train_loaders, val_loaders
    Main->>TrainLoop: 执行训练循环
    TrainLoop->>Loss: 计算基础损失
    Loss-->>TrainLoop: loss_value, loss_stats
    TrainLoop->>Save: 保存检查点
    Save-->>TrainLoop: 保存完成
    TrainLoop-->>Main: 训练完成
```

## 数据实体结构深化

```mermaid
erDiagram
    TrainingBatch {
        tensor input_images "形状[B,3,768,768]"
        dict targets "包含hm,wh,reg等目标"
        dict metadata "图像元信息"
    }
    
    ModelOutput {
        dict predictions "6个检测头输出"
        tensor hm "热力图[B,2,192,192]"
        tensor wh "边界框[B,8,192,192]"
        tensor reg "偏移[B,2,192,192]"
        tensor st "结构[B,8,192,192]"
        tensor ax "轴向[B,256,192,192]"
        tensor cr "角点[B,256,192,192]"
    }
    
    LossOutput {
        tensor total_loss "总损失值"
        dict loss_stats "各项损失统计"
        float hm_loss "热力图损失"
        float wh_loss "边界框损失"
        float reg_loss "偏移损失"
    }
    
    TrainingBatch ||--|| ModelOutput : "前向传播"
    ModelOutput ||--|| LossOutput : "损失计算"
```

## 配置项

基于已存在的`lore_tsr_config.yaml`，迭代3重点关注以下配置项：

- `training.epochs`: 训练轮数（默认90）
- `training.batch_size`: 批次大小（默认32）
- `training.optimizer`: 优化器配置（Adam，学习率1.25e-4）
- `data.processing.image_size`: 图像尺寸[768,768]
- `model.heads`: 6个检测头配置
- `distributed.mixed_precision`: 混合精度训练

## 模块化文件详解 (File-by-File Breakdown)

### training_loops/table_structure_recognition/train_lore_tsr.py

**a. 文件用途说明**
完善LORE-TSR训练循环的核心实现，集成accelerate分布式训练框架，实现基础的训练、验证和检查点保存逻辑。

**b. 文件内类图**
```mermaid
classDiagram
    class TrainingLoop {
        +run_training_loop()
        +train_one_epoch()
        +validate_one_epoch()
        +save_checkpoint()
    }
    
    class DataManager {
        +prepare_dataloaders()
        +create_dummy_batch()
    }
    
    class ModelManager {
        +create_model_and_ema()
        +prepare_model_for_training()
    }
    
    TrainingLoop --> DataManager
    TrainingLoop --> ModelManager
```

**c. 函数/方法详解**

#### run_training_loop()
- **用途**: 执行完整的训练循环，包括多轮训练和验证
- **输入参数**: 
  - `config`: 配置对象
  - `accelerator`: accelerate对象
  - `model`: LORE-TSR模型
  - `ema_handler`: EMA处理器
  - `optimizer`: 优化器
  - `lr_scheduler`: 学习率调度器
  - `train_loaders`: 训练数据加载器列表
  - `val_loaders`: 验证数据加载器列表
- **输出数据结构**: 无返回值，执行训练过程
- **实现流程**:
```mermaid
flowchart TD
    A[开始训练循环] --> B[遍历每个epoch]
    B --> C[执行训练阶段]
    C --> D[计算训练损失]
    D --> E[反向传播和优化]
    E --> F[更新EMA]
    F --> G{是否验证epoch}
    G -->|是| H[执行验证阶段]
    G -->|否| I[保存检查点]
    H --> I
    I --> J{是否完成所有epoch}
    J -->|否| B
    J -->|是| K[训练完成]
```

#### prepare_dataloaders()
- **用途**: 准备训练和验证数据加载器，当前使用虚拟数据
- **输入参数**:
  - `config`: 配置对象
  - `mode`: 模式('train'或'val')
  - `batch_size`: 批次大小
- **输出数据结构**: `List[Tuple[str, DataLoader]]` - 数据加载器列表
- **实现流程**:
```mermaid
flowchart TD
    A[接收配置参数] --> B[创建虚拟数据集]
    B --> C[应用数据变换]
    C --> D[创建DataLoader]
    D --> E[返回数据加载器列表]
```

### networks/lore_tsr/lore_tsr_loss.py

**a. 文件用途说明**
实现LORE-TSR的基础损失函数，当前提供简化版本，为迭代4的完整损失函数实现预留接口。

**b. 文件内类图**
```mermaid
classDiagram
    class LoreTsrBasicLoss {
        +__init__(config)
        +forward(predictions, targets)
        +compute_hm_loss()
        +compute_wh_loss()
        +compute_reg_loss()
    }
    
    class FocalLoss {
        +forward(pred, gt)
    }
    
    class RegL1Loss {
        +forward(pred, gt, mask)
    }
    
    LoreTsrBasicLoss --> FocalLoss
    LoreTsrBasicLoss --> RegL1Loss
```

**c. 函数/方法详解**

#### forward()
- **用途**: 计算LORE-TSR的基础损失函数
- **输入参数**:
  - `predictions`: 模型预测输出字典
  - `targets`: 目标标签字典
- **输出数据结构**: `(total_loss: Tensor, loss_stats: Dict)`
- **实现流程**:
```mermaid
flowchart TD
    A[接收预测和目标] --> B[计算热力图损失]
    B --> C[计算边界框损失]
    C --> D[计算偏移损失]
    D --> E[加权求和总损失]
    E --> F[返回损失和统计]
```

### my_datasets/table_structure_recognition/lore_tsr_dataset.py

**a. 文件用途说明**
实现LORE-TSR数据集类，继承train-anything框架的TableDataset基类，支持分布式标注格式，当前使用虚拟数据进行训练循环测试。

**b. 文件内类图**
```mermaid
classDiagram
    class TableDataset {
        +__init__(data_root, mode, ...)
        +__len__()
        +__getitem__(index)
        +_load_distributed_annotations()
        +_parse_cell_bbox()
    }

    class LoreTsrDataset {
        +__init__(config, mode)
        +_prepare_lore_targets()
        +_generate_dummy_annotation()
        +_create_heatmap_targets()
        +_create_bbox_targets()
    }

    class DummyDataUtils {
        +create_dummy_part_structure()
        +generate_wtw_format_annotation()
        +create_quality_valid_sample()
    }

    TableDataset <|-- LoreTsrDataset
    LoreTsrDataset --> DummyDataUtils
```

**c. 函数/方法详解**

#### __getitem__()
- **用途**: 获取单个训练样本，继承TableDataset基础功能，添加LORE-TSR特定的目标准备
- **输入参数**: `index: int` - 样本索引
- **输出数据结构**: `Dict` - 包含input、targets、metadata的样本字典
- **实现流程**:
```mermaid
flowchart TD
    A[接收样本索引] --> B[调用父类获取基础样本]
    B --> C[解析WTW格式标注]
    C --> D[生成LORE-TSR目标格式]
    D --> E[创建热力图目标]
    E --> F[创建边界框目标]
    F --> G[应用数据变换]
    G --> H[返回样本字典]
```

#### _generate_dummy_annotation()
- **用途**: 生成符合WTW格式的虚拟标注数据，用于迭代3测试
- **输入参数**: `image_size: Tuple[int, int]` - 图像尺寸
- **输出数据结构**: `Dict` - WTW格式的标注数据
- **实现流程**:
```mermaid
flowchart TD
    A[接收图像尺寸] --> B[创建虚拟表格结构]
    B --> C[生成单元格bbox坐标]
    C --> D[设置逻辑位置lloc]
    D --> E[添加quality='合格'字段]
    E --> F[返回WTW格式标注]
```

### my_datasets/table_structure_recognition/lore_tsr_transforms.py

**a. 文件用途说明**
实现LORE-TSR数据变换和预处理，包括图像归一化、尺寸调整和数据增强。

**b. 文件内类图**
```mermaid
classDiagram
    class LoreTsrTransforms {
        +__init__(config)
        +__call__(sample)
        +normalize_image()
        +resize_image()
        +apply_augmentation()
    }

    class ImageProcessor {
        +to_tensor()
        +normalize()
        +resize()
    }

    LoreTsrTransforms --> ImageProcessor
```

**c. 函数/方法详解**

#### __call__()
- **用途**: 对输入样本应用完整的数据变换流程
- **输入参数**: `sample: Dict` - 原始样本数据
- **输出数据结构**: `Dict` - 变换后的样本数据
- **实现流程**:
```mermaid
sequenceDiagram
    participant T as Transforms
    participant I as ImageProcessor
    participant A as Augmentation

    T->>I: 图像预处理
    I-->>T: 处理后图像
    T->>A: 数据增强
    A-->>T: 增强后数据
    T->>T: 目标适配
    T-->>T: 返回样本
```

### modules/utils/lore_tsr/dummy_data_utils.py

**a. 文件用途说明**
提供虚拟数据生成工具，创建符合train-anything框架TableDataset格式的虚拟数据结构，支持WTW标注格式和分布式目录组织。

**b. 文件内类图**
```mermaid
classDiagram
    class DummyDataUtils {
        +create_dummy_part_structure()
        +generate_wtw_format_annotation()
        +create_quality_valid_sample()
        +setup_dummy_dataset_structure()
    }

    class WTWAnnotationGenerator {
        +generate_table_cells()
        +create_cell_bbox()
        +create_logical_location()
        +set_quality_field()
    }

    DummyDataUtils --> WTWAnnotationGenerator
```

**c. 函数/方法详解**

#### create_dummy_part_structure()
- **用途**: 创建符合train-anything数据集组织的虚拟part目录结构
- **输入参数**: `base_dir: str, num_parts: int, samples_per_part: int`
- **输出数据结构**: `List[str]` - 创建的part目录路径列表
- **实现流程**:
```mermaid
flowchart TD
    A[接收目录参数] --> B[创建part_0001目录]
    B --> C[生成虚拟图像文件]
    C --> D[创建对应JSON标注]
    D --> E[设置quality='合格']
    E --> F[重复创建其他part]
    F --> G[返回目录列表]
```

#### generate_wtw_format_annotation()
- **用途**: 生成符合WTW格式的单个样本标注数据
- **输入参数**: `table_size: Tuple[int, int], image_size: Tuple[int, int]`
- **输出数据结构**: `Dict` - 包含cells、quality等字段的WTW格式标注
- **实现流程**:
```mermaid
sequenceDiagram
    participant Utils as DummyDataUtils
    participant Gen as WTWAnnotationGenerator
    participant Cell as CellGenerator

    Utils->>Gen: 生成表格标注
    Gen->>Cell: 创建单元格列表
    Cell-->>Gen: cells数据
    Gen->>Gen: 设置bbox坐标(p1,p2,p3,p4)
    Gen->>Gen: 设置lloc逻辑位置
    Gen->>Gen: 设置quality='合格'
    Gen-->>Utils: WTW格式标注
```

## 迭代演进依据

### 架构扩展性
1. **损失函数扩展**: `LoreTsrBasicLoss`为迭代4的完整损失函数预留了清晰接口
2. **数据集适配**: `LoreTsrDataset`继承`TableDataset`，支持从虚拟数据平滑过渡到真实WTW格式数据
3. **训练循环增强**: 当前训练循环支持后续添加更复杂的验证指标和可视化

### 模块化设计
1. **继承复用**: 充分利用train-anything现有的`TableDataset`基础设施
2. **标准格式**: 严格遵循WTW标注格式和分布式目录组织
3. **配置驱动**: 所有行为通过配置文件控制，支持灵活调整
4. **接口标准化**: 遵循train-anything框架标准，确保兼容性

### 数据组织规范
遵循train-anything框架的数据集组织方式：
```
data_root/
├── part_0001/
│   ├── image_001.jpg
│   ├── image_001.json          # 或 image_001_table_annotation.json
│   ├── image_002.png
│   ├── image_002.json
│   └── ...
├── part_0002/
│   ├── image_003.jpg
│   ├── image_003.json
│   └── ...
└── ...
```

### 后期迭代支持
1. **迭代4**: 损失函数完整实现 - 直接替换`LoreTsrBasicLoss`
2. **迭代5**: 数据集适配器 - 替换虚拟数据为真实WTW格式数据加载
3. **迭代6**: Processor组件 - 在训练循环中集成逻辑结构恢复

## 如何迁移 LORE-TSR

### 代码文件对应关系

| LORE-TSR源文件 | train-anything目标文件 | 迁移状态 | 说明 |
|---------------|----------------------|---------|------|
| `main.py` | `train_lore_tsr.py` | 🔄 **迭代3实现** | 训练循环主逻辑 |
| `lib/models/losses.py` | `lore_tsr_loss.py` | 🔄 **迭代3基础版** | 简化损失函数 |
| `lib/datasets/dataset/table_mid.py` | `lore_tsr_dataset.py` | 🔄 **迭代3框架** | 继承TableDataset，支持WTW格式 |
| `lib/models/model.py` | `lore_tsr_model.py` | ✅ **已完成** | 模型工厂 |
| `lib/opts.py` | `lore_tsr_config.yaml` | ✅ **已完成** | 配置系统 |

### 迁移策略
1. **保持核心算法**: 模型架构和损失计算逻辑完全保持不变
2. **适配框架接口**: 训练循环和数据加载适配train-anything标准
3. **继承现有基础**: 充分利用TableDataset的成熟数据处理能力
4. **标准格式支持**: 完全支持WTW标注格式和分布式目录组织
5. **渐进式实现**: 从基础功能开始，逐步完善复杂特性

### 数据格式适配说明
- **源格式**: LORE-TSR使用COCO格式标注
- **目标格式**: train-anything使用WTW分布式JSON格式
- **关键字段映射**:
  - `bbox` → `bbox.p1/p2/p3/p4` (四点坐标)
  - `logic_axis` → `lloc.start_row/end_row/start_col/end_col` (逻辑位置)
  - 新增 `quality` 字段，只加载"合格"样本
- **目录组织**: 从单一标注文件改为分part的分布式结构

---

**文档版本**: v1.0
**创建日期**: 2025-07-20
**迭代范围**: 迭代3 - 基础训练循环
**依赖迭代**: 迭代1,2（已完成）
**后续迭代**: 迭代4（损失函数完整迁移）
