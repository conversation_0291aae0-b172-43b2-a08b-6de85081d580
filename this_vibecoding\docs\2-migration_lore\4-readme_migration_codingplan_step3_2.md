# LORE-TSR 迁移编码计划 - 迭代3步骤3.2

## 📋 计划概述

**当前迭代**: 迭代3 - 基础训练循环  
**步骤标识**: 步骤3.2 - 完善训练循环稳定性和完整性  
**迁移策略**: 重构适配框架入口 + 复制保留核心算法  
**核心目标**: 在步骤3.1基础上，完善训练循环的稳定性，添加验证逻辑、指标记录、目标准备等，确保训练过程更加健壮

## 🗺️ 动态迁移蓝图

### 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | ✅ **已完成** |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | 🔄 **进行中** |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留：模型工厂函数 | 迭代2 | **复杂** | ✅ **已完成** |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | 简单 | 🔄 **基础版完成** |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留：主要骨干网络 | 迭代2 | 简单 | ✅ **已完成** |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配：数据集适配器 | 迭代5 | **复杂** | 🔄 **框架版完成** |
| `src/lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留：后处理工具 | 迭代11 | 简单 | `未开始` |
| `src/lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离：可变形卷积 | 迭代7 | 简单 | `未开始` |
| `src/lib/models/networks/NMS/` | `external/lore_tsr/NMS/` | 复制隔离：非极大值抑制 | 迭代7 | 简单 | `未开始` |

**状态说明:**
- ✅ **已完成**: 迭代1,2已完成的文件
- 🔄 **进行中**: 当前步骤3.2正在完善的文件
- 🔄 **基础版完成**: 步骤3.1完成的基础版本，待后续迭代完善
- 🔄 **框架版完成**: 步骤3.1完成的框架版本，待后续迭代完善
- `未开始`: 后续迭代将处理的文件

### 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代3步骤3.2 - 完善训练循环稳定性

    subgraph "步骤3.1已完成"
        S31_1["基础损失函数"]
        S31_2["基础训练循环"]
        S31_3["数据集框架"]
    end

    subgraph "步骤3.2目标增强"
        direction LR
        T1["目标准备逻辑完善"]
        T2["验证循环添加"]
        T3["指标记录系统"]
        T4["训练稳定性增强"]
    end

    subgraph "Target: train-anything (步骤3.2)"
        F1["lore_tsr_target_preparation.py (完善)"]
        F2["train_lore_tsr.py (增强验证循环)"]
        F3["lore_tsr_transforms.py (完善预处理)"]
        F4["lore_tsr_dataset.py (增强目标准备)"]
    end

    %% 依赖关系
    S31_1 -.-> T1
    S31_2 -.-> T2
    S31_2 -.-> T3
    S31_3 -.-> T4

    %% 迁移映射
    T1 --> F1
    T2 --> F2
    T3 --> F2
    T4 --> F3
    T1 --> F4
```

## 📁 目标目录结构树

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                     # ✅ 已存在
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                        # 🔄 待增强验证循环
├── networks/lore_tsr/
│   ├── __init__.py                              # ✅ 已存在
│   ├── lore_tsr_model.py                        # ✅ 已存在
│   ├── lore_tsr_loss.py                         # 🔄 基础版已完成
│   ├── backbones/                               # ✅ 已存在
│   └── heads/                                   # ✅ 已存在
├── my_datasets/table_structure_recognition/
│   ├── lore_tsr_dataset.py                      # 🔄 待增强目标准备
│   ├── lore_tsr_transforms.py                   # 🔄 待完善预处理
│   └── lore_tsr_target_preparation.py           # 🔄 待实现基础逻辑
└── modules/utils/lore_tsr/
    ├── __init__.py                              # ✅ 已存在
    └── dummy_data_utils.py                      # ✅ 已存在
```

## 🔧 具体编码步骤

### 步骤标题
**迭代3步骤3.2: 完善训练循环稳定性和完整性**

### 当前迭代
迭代3 - 基础训练循环

### 影响文件
1. `my_datasets/table_structure_recognition/lore_tsr_target_preparation.py` - 实现基础目标准备逻辑
2. `my_datasets/table_structure_recognition/lore_tsr_dataset.py` - 增强目标准备集成
3. `my_datasets/table_structure_recognition/lore_tsr_transforms.py` - 完善数据预处理
4. `training_loops/table_structure_recognition/train_lore_tsr.py` - 添加验证循环和指标记录

### 具体操作

#### 操作1: 实现基础目标准备逻辑
**文件**: `my_datasets/table_structure_recognition/lore_tsr_target_preparation.py`
**策略**: 复制保留核心算法（基础版本）

```python
#!/usr/bin/env python3
"""
LORE-TSR 目标准备模块

迭代3步骤3.2：实现基础目标准备逻辑
迭代5：将扩展为完整的目标准备逻辑
"""

import torch
import numpy as np
import math
from typing import Dict, List, Tuple, Any

def prepare_lore_tsr_targets(annotation: Dict, config) -> Dict:
    """
    准备LORE-TSR训练目标
    
    迭代3步骤3.2：基础实现
    迭代5：完整实现
    
    Args:
        annotation: WTW格式标注
        config: 配置对象
        
    Returns:
        Dict: LORE-TSR目标格式
    """
    input_h, input_w = config.data.input_h, config.data.input_w
    output_h, output_w = input_h // config.data.down_ratio, input_w // config.data.down_ratio
    num_classes = config.model.num_classes
    max_objs = config.model.get('max_objs', 500)
    
    # 提取单元格信息
    cells = annotation.get('cells', [])
    
    # 创建目标张量
    targets = {
        'hm': torch.zeros(num_classes, output_h, output_w),
        'wh': torch.zeros(max_objs, 8),
        'reg': torch.zeros(max_objs, 2),
        'reg_mask': torch.zeros(max_objs),
        'ind': torch.zeros(max_objs).long(),
        'num_objs': len(cells)
    }
    
    # 处理每个单元格
    for idx, cell in enumerate(cells[:max_objs]):
        # 提取边界框坐标
        bbox = cell.get('bbox', {})
        if not bbox:
            continue
            
        # 获取四个角点
        p1 = bbox.get('p1', [0, 0])
        p2 = bbox.get('p2', [0, 0])
        p3 = bbox.get('p3', [0, 0])
        p4 = bbox.get('p4', [0, 0])
        
        # 计算中心点
        center_x = (p1[0] + p2[0] + p3[0] + p4[0]) / 4.0
        center_y = (p1[1] + p2[1] + p3[1] + p4[1]) / 4.0
        
        # 缩放到输出尺寸
        center_x = center_x * output_w / input_w
        center_y = center_y * output_h / input_h
        
        # 确保在边界内
        center_x = max(0, min(center_x, output_w - 1))
        center_y = max(0, min(center_y, output_h - 1))
        
        # 计算整数坐标和偏移
        ct_int_x, ct_int_y = int(center_x), int(center_y)
        ct_offset_x, ct_offset_y = center_x - ct_int_x, center_y - ct_int_y
        
        # 设置热力图目标（类别0为单元格）
        targets['hm'][0, ct_int_y, ct_int_x] = 1.0
        
        # 设置回归目标
        targets['reg'][idx, 0] = ct_offset_x
        targets['reg'][idx, 1] = ct_offset_y
        targets['reg_mask'][idx] = 1.0
        
        # 设置索引
        targets['ind'][idx] = ct_int_y * output_w + ct_int_x
        
        # 设置边界框目标（8个坐标值）
        # 缩放四个角点到输出尺寸
        scaled_points = []
        for point in [p1, p2, p3, p4]:
            scaled_x = point[0] * output_w / input_w
            scaled_y = point[1] * output_h / input_h
            scaled_points.extend([scaled_x - center_x, scaled_y - center_y])
        
        targets['wh'][idx] = torch.tensor(scaled_points[:8])
    
    return targets

def create_heatmap_targets(cells: List[Dict], output_size: Tuple[int, int], num_classes: int) -> torch.Tensor:
    """
    创建热力图目标
    
    Args:
        cells: 单元格列表
        output_size: 输出尺寸 (height, width)
        num_classes: 类别数量
        
    Returns:
        torch.Tensor: 热力图目标 [num_classes, H, W]
    """
    output_h, output_w = output_size
    heatmap = torch.zeros(num_classes, output_h, output_w)
    
    for cell in cells:
        bbox = cell.get('bbox', {})
        if not bbox:
            continue
            
        # 计算中心点
        points = [bbox.get(f'p{i}', [0, 0]) for i in range(1, 5)]
        center_x = sum(p[0] for p in points) / 4.0
        center_y = sum(p[1] for p in points) / 4.0
        
        # 转换到输出坐标系
        ct_x = int(center_x * output_w / 768)  # 假设输入尺寸768
        ct_y = int(center_y * output_h / 768)
        
        # 确保在边界内
        if 0 <= ct_x < output_w and 0 <= ct_y < output_h:
            heatmap[0, ct_y, ct_x] = 1.0  # 类别0为单元格
    
    return heatmap

def create_bbox_targets(cells: List[Dict], output_size: Tuple[int, int]) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    创建边界框回归目标
    
    Args:
        cells: 单元格列表
        output_size: 输出尺寸 (height, width)
        
    Returns:
        Tuple[torch.Tensor, torch.Tensor]: (边界框目标, 掩码)
    """
    max_objs = 500
    bbox_targets = torch.zeros(max_objs, 8)
    bbox_mask = torch.zeros(max_objs)
    
    for idx, cell in enumerate(cells[:max_objs]):
        bbox = cell.get('bbox', {})
        if not bbox:
            continue
            
        # 提取四个角点并展平
        points = []
        for i in range(1, 5):
            point = bbox.get(f'p{i}', [0, 0])
            points.extend(point)
        
        bbox_targets[idx] = torch.tensor(points[:8])
        bbox_mask[idx] = 1.0
    
    return bbox_targets, bbox_mask
```

#### 操作2: 增强数据集目标准备集成
**文件**: `my_datasets/table_structure_recognition/lore_tsr_dataset.py`
**策略**: 重构适配框架入口

需要修改`_prepare_lore_targets`方法，集成新的目标准备逻辑：

```python
def _prepare_lore_targets(self, annotation: Dict) -> Dict:
    """
    准备LORE-TSR特定的目标格式
    
    Args:
        annotation: WTW格式标注
        
    Returns:
        Dict: LORE-TSR目标格式
    """
    # 使用新的目标准备逻辑
    from my_datasets.table_structure_recognition.lore_tsr_target_preparation import prepare_lore_tsr_targets
    
    targets = prepare_lore_tsr_targets(annotation, self.config)
    
    return targets
```

#### 操作3: 完善数据预处理
**文件**: `my_datasets/table_structure_recognition/lore_tsr_transforms.py`
**策略**: 重构适配框架入口

增强数据变换，添加更好的错误处理和数据验证：

```python
def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
    """
    对输入样本应用完整的数据变换流程
    
    Args:
        sample: 原始样本数据
        
    Returns:
        Dict: 变换后的样本数据
    """
    try:
        # 图像预处理
        if 'input' in sample:
            input_data = sample['input']
            
            # 处理不同的输入格式
            if isinstance(input_data, np.ndarray):
                input_data = torch.from_numpy(input_data)
            elif isinstance(input_data, str):
                # 如果是文件路径，创建虚拟数据
                input_data = torch.randn(3, self.input_h, self.input_w)
            
            # 确保数据类型和形状正确
            if input_data.dim() == 3 and input_data.shape[0] != 3:
                input_data = input_data.permute(2, 0, 1)  # [H, W, C] -> [C, H, W]
            
            # 归一化到[0,1]范围
            if input_data.max() > 1.0:
                input_data = input_data.float() / 255.0
            
            # 调整尺寸
            if input_data.shape[-2:] != (self.input_h, self.input_w):
                input_data = torch.nn.functional.interpolate(
                    input_data.unsqueeze(0),
                    size=(self.input_h, self.input_w),
                    mode='bilinear',
                    align_corners=False
                ).squeeze(0)
            
            sample['input'] = input_data
            
        # 验证数据完整性
        self._validate_sample(sample)
        
    except Exception as e:
        # 错误处理：创建默认样本
        logger.warning(f"数据变换失败，使用默认样本: {e}")
        sample = self._create_default_sample()
    
    return sample

def _validate_sample(self, sample: Dict[str, Any]):
    """验证样本数据完整性"""
    if 'input' not in sample:
        raise ValueError("样本缺少input字段")
    
    input_shape = sample['input'].shape
    if len(input_shape) != 3 or input_shape[0] != 3:
        raise ValueError(f"输入形状错误: {input_shape}")
    
    if input_shape[1:] != (self.input_h, self.input_w):
        raise ValueError(f"输入尺寸错误: {input_shape[1:]}")

def _create_default_sample(self) -> Dict[str, Any]:
    """创建默认样本"""
    return {
        'input': torch.randn(3, self.input_h, self.input_w),
        'targets': {},
        'metadata': {'is_default': True}
    }
```

### 受影响的现有模块
- 无直接影响，主要是对现有模块的增强

### 复用已有代码
- 继续使用train-anything框架的TableDataset基类
- 复用accelerate训练循环框架
- 复用步骤3.1建立的基础设施

### 如何验证 (Verification)

#### 验证命令1: 目标准备逻辑测试
```bash
cd train-anything
python -c "
import torch
import sys
sys.path.append('.')
from my_datasets.table_structure_recognition.lore_tsr_target_preparation import prepare_lore_tsr_targets, create_heatmap_targets
from omegaconf import DictConfig

# 创建测试配置
config = DictConfig({
    'data': {'input_h': 768, 'input_w': 768, 'down_ratio': 4},
    'model': {'num_classes': 2, 'max_objs': 500}
})

# 创建测试标注
annotation = {
    'cells': [
        {
            'bbox': {
                'p1': [100, 100], 'p2': [200, 100],
                'p3': [200, 200], 'p4': [100, 200]
            }
        }
    ]
}

# 测试目标准备
targets = prepare_lore_tsr_targets(annotation, config)
print('✅ 目标准备成功')
print(f'热力图形状: {targets[\"hm\"].shape}')
print(f'边界框形状: {targets[\"wh\"].shape}')
print(f'有效目标数: {targets[\"num_objs\"]}')
"
```

#### 验证命令2: 增强数据集测试
```bash
cd train-anything
python -c "
import sys
sys.path.append('.')
from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
from omegaconf import DictConfig

config = DictConfig({
    'data': {'input_h': 768, 'input_w': 768, 'down_ratio': 4, 'num_workers': 0},
    'model': {'num_classes': 2, 'max_objs': 500}
})

dataset = LoreTsrDataset(config, mode='train')
print(f'✅ 数据集创建成功，大小: {len(dataset)}')

# 测试样本获取
sample = dataset[0]
print(f'✅ 样本获取成功')
print(f'输入形状: {sample[\"input\"].shape}')
print(f'目标键: {list(sample[\"targets\"].keys())}')
"
```

#### 操作4: 添加验证循环和指标记录
**文件**: `training_loops/table_structure_recognition/train_lore_tsr.py`
**策略**: 重构适配框架入口

需要修改`run_training_loop`函数，添加验证循环和更完善的指标记录：

```python
def run_training_loop(config, model, accelerator, ema_handler, loss_criterion,
                     optimizer, lr_scheduler, weight_dtype, train_loaders, val_loaders,
                     global_step, first_epoch, max_train_steps, best_loss_model_record, best_loss_record_data):
    """执行完整的训练循环（迭代3步骤3.2：增强版）"""

    logger.info("开始LORE-TSR训练循环（迭代3步骤3.2：增强版）")

    # 训练指标记录
    best_val_loss = float('inf')
    train_losses = []
    val_losses = []

    for epoch in range(first_epoch, config.training.epochs):
        logger.info(f"训练 Epoch {epoch + 1}/{config.training.epochs}")

        # ===== 训练阶段 =====
        model.train()
        epoch_train_loss = 0.0
        epoch_train_stats = {}
        num_train_batches = 0

        for loader_name, train_loader in train_loaders:
            for batch_idx, batch in enumerate(train_loader):
                try:
                    # 前向传播
                    with accelerator.autocast():
                        predictions = model(batch['input'])
                        total_loss, loss_stats = loss_criterion(predictions, batch['targets'])

                    # 反向传播
                    accelerator.backward(total_loss)

                    # 梯度裁剪（可选）
                    if config.training.get('max_grad_norm', None):
                        accelerator.clip_grad_norm_(model.parameters(), config.training.max_grad_norm)

                    optimizer.step()
                    optimizer.zero_grad()

                    # 更新EMA
                    if ema_handler is not None:
                        ema_handler.update(model)

                    # 累积损失统计
                    epoch_train_loss += total_loss.item()
                    for key, value in loss_stats.items():
                        if key not in epoch_train_stats:
                            epoch_train_stats[key] = 0.0
                        epoch_train_stats[key] += value

                    num_train_batches += 1
                    global_step += 1

                    # 定期记录训练指标
                    if global_step % 50 == 0:
                        current_lr = optimizer.param_groups[0]['lr']
                        logger.info(f"Step {global_step}, Loss: {total_loss.item():.4f}, LR: {current_lr:.6f}")

                        # 记录到accelerator tracker
                        if accelerator.is_main_process:
                            accelerator.log({
                                "train/loss": total_loss.item(),
                                "train/learning_rate": current_lr,
                                "train/global_step": global_step
                            }, step=global_step)

                    # 检查是否达到最大步数
                    if global_step >= max_train_steps:
                        break

                except Exception as e:
                    logger.warning(f"训练步骤失败，跳过: {e}")
                    continue

            if global_step >= max_train_steps:
                break

        # 计算训练平均损失
        avg_train_loss = epoch_train_loss / max(num_train_batches, 1)
        train_losses.append(avg_train_loss)

        # 计算平均训练统计
        avg_train_stats = {}
        for key, value in epoch_train_stats.items():
            avg_train_stats[key] = value / max(num_train_batches, 1)

        logger.info(f"Epoch {epoch + 1} 训练平均损失: {avg_train_loss:.4f}")

        # ===== 验证阶段 =====
        if val_loaders and (epoch + 1) % config.training.get('val_freq', 1) == 0:
            val_loss, val_stats = validate_one_epoch(
                model, val_loaders, loss_criterion, accelerator, weight_dtype
            )
            val_losses.append(val_loss)

            logger.info(f"Epoch {epoch + 1} 验证平均损失: {val_loss:.4f}")

            # 记录验证指标
            if accelerator.is_main_process:
                log_data = {
                    "val/loss": val_loss,
                    "train/epoch_loss": avg_train_loss,
                    "train/epoch": epoch + 1
                }
                log_data.update({f"val/{k}": v for k, v in val_stats.items()})
                log_data.update({f"train/{k}": v for k, v in avg_train_stats.items()})
                accelerator.log(log_data, step=global_step)

            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                logger.info(f"发现更好的模型，验证损失: {val_loss:.4f}")

                if accelerator.is_main_process:
                    save_best_model(
                        config, model, optimizer, lr_scheduler, accelerator,
                        ema_handler, global_step, val_loss, best_loss_model_record
                    )

        # 学习率调度
        lr_scheduler.step()

        # 定期保存检查点
        if (epoch + 1) % config.training.get('save_freq', 10) == 0:
            if accelerator.is_main_process:
                save_checkpoint(
                    config, model, optimizer, lr_scheduler, accelerator,
                    ema_handler, global_step, epoch + 1
                )

        if global_step >= max_train_steps:
            break

    logger.info(f"训练循环完成，最终步数: {global_step}")
    logger.info(f"最佳验证损失: {best_val_loss:.4f}")

    return global_step

def validate_one_epoch(model, val_loaders, loss_criterion, accelerator, weight_dtype):
    """执行一个验证epoch"""
    model.eval()
    total_val_loss = 0.0
    total_val_stats = {}
    num_val_batches = 0

    with torch.no_grad():
        for loader_name, val_loader in val_loaders:
            for batch in val_loader:
                try:
                    with accelerator.autocast():
                        predictions = model(batch['input'])
                        val_loss, val_stats = loss_criterion(predictions, batch['targets'])

                    total_val_loss += val_loss.item()
                    for key, value in val_stats.items():
                        if key not in total_val_stats:
                            total_val_stats[key] = 0.0
                        total_val_stats[key] += value

                    num_val_batches += 1

                except Exception as e:
                    logger.warning(f"验证步骤失败，跳过: {e}")
                    continue

    # 计算平均值
    avg_val_loss = total_val_loss / max(num_val_batches, 1)
    avg_val_stats = {}
    for key, value in total_val_stats.items():
        avg_val_stats[key] = value / max(num_val_batches, 1)

    return avg_val_loss, avg_val_stats

def save_best_model(config, model, optimizer, lr_scheduler, accelerator,
                   ema_handler, global_step, val_loss, best_loss_model_record):
    """保存最佳模型"""
    try:
        save_path = os.path.join(config.basic.output_dir, "best_model")
        os.makedirs(save_path, exist_ok=True)

        # 保存模型状态
        accelerator.save_state(save_path)

        # 更新最佳模型记录
        record_data = {
            "global_step": global_step,
            "val_loss": val_loss,
            "save_path": save_path,
            "timestamp": datetime.datetime.now().isoformat()
        }

        with open(best_loss_model_record, 'w') as f:
            json.dump(record_data, f, indent=2)

        logger.info(f"最佳模型已保存到: {save_path}")

    except Exception as e:
        logger.error(f"保存最佳模型失败: {e}")

def save_checkpoint(config, model, optimizer, lr_scheduler, accelerator,
                   ema_handler, global_step, epoch):
    """保存训练检查点"""
    try:
        checkpoint_path = os.path.join(config.basic.output_dir, f"checkpoint_epoch_{epoch}")
        os.makedirs(checkpoint_path, exist_ok=True)

        accelerator.save_state(checkpoint_path)

        logger.info(f"检查点已保存到: {checkpoint_path}")

    except Exception as e:
        logger.error(f"保存检查点失败: {e}")
```

#### 操作5: 完善模型保存逻辑
**文件**: `training_loops/table_structure_recognition/train_lore_tsr.py`
**策略**: 重构适配框架入口

修改`save_final_model`函数：

```python
def save_final_model(config, model, optimizer, lr_scheduler, accelerator, ema_handler, global_step):
    """
    保存最终模型

    迭代3步骤3.2：实现完整的模型保存逻辑
    """
    logger.info("保存最终模型（迭代3步骤3.2：完整实现）")

    accelerator.wait_for_everyone()

    if accelerator.is_main_process:
        try:
            # 保存最终模型
            final_save_path = os.path.join(config.basic.output_dir, "final_model")
            os.makedirs(final_save_path, exist_ok=True)

            accelerator.save_state(final_save_path)

            # 保存模型信息
            model_info = {
                "global_step": global_step,
                "model_arch": config.model.arch_name,
                "num_classes": config.model.num_classes,
                "input_size": [config.data.input_h, config.data.input_w],
                "training_completed": True,
                "save_timestamp": datetime.datetime.now().isoformat()
            }

            info_path = os.path.join(final_save_path, "model_info.json")
            with open(info_path, 'w') as f:
                json.dump(model_info, f, indent=2)

            logger.info(f"最终模型已保存到: {final_save_path}")
            logger.info(f"模型信息已保存到: {info_path}")

        except Exception as e:
            logger.error(f"保存最终模型失败: {e}")
```

#### 验证命令3: 完整训练循环增强测试
```bash
cd train-anything
python -c "
import torch
import sys
sys.path.append('.')
from training_loops.table_structure_recognition.train_lore_tsr import validate_one_epoch
from networks.lore_tsr.lore_tsr_loss import LoreTsrBasicLoss
from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
from omegaconf import DictConfig
from accelerate import Accelerator

# 创建测试配置
config = DictConfig({
    'data': {'input_h': 768, 'input_w': 768, 'down_ratio': 4, 'num_workers': 0},
    'model': {'arch_name': 'resfpnhalf', 'num_classes': 2, 'max_objs': 500, 'head_conv': 64, 'heads': {'hm': 2, 'wh': 8, 'reg': 2, 'st': 8, 'ax': 256, 'cr': 256}},
    'loss': {'weights': {'hm_weight': 1.0, 'wh_weight': 1.0, 'off_weight': 1.0}},
    'training': {'batch_size': 2}
})

# 创建组件
accelerator = Accelerator()
model = create_lore_tsr_model(config)
loss_criterion = LoreTsrBasicLoss(config)
dataset = LoreTsrDataset(config, mode='train')

# 创建数据加载器
dataloader = torch.utils.data.DataLoader(dataset, batch_size=2, shuffle=False)
val_loaders = [('test', dataloader)]

print('✅ 组件创建成功')

# 测试验证循环
val_loss, val_stats = validate_one_epoch(model, val_loaders, loss_criterion, accelerator, torch.float32)
print(f'✅ 验证循环测试成功')
print(f'验证损失: {val_loss:.4f}')
print(f'验证统计: {val_stats}')
"
```

#### 验证命令4: 完整功能集成测试
```bash
cd train-anything
python training_loops/table_structure_recognition/train_lore_tsr.py \
    --config configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml \
    --debug \
    --epochs 2 \
    --batch_size 2
```

**预期输出**:
- 训练循环正常运行2个epoch
- 验证循环正常执行
- 指标记录正常工作
- 模型保存功能正常
- 错误处理机制有效

### 当前迭代逻辑图
参见上方"动态迁移蓝图"部分的Mermaid图

---

**计划制定时间**: 2025-07-20
**目标迭代**: 迭代3步骤3.2
**预估完成时间**: 2-3小时
**验证要求**: 目标准备逻辑正常工作，数据预处理更加稳定，训练循环更加健壮，验证循环和指标记录正常
**下一步骤**: 迭代4步骤4.1 - 完整损失函数迁移

## 🚨 重要提醒

1. **保持迭代3范围**: 当前仍在迭代3范围内，专注于训练循环的稳定性和完整性
2. **不涉及完整损失函数**: 完整的6个损失组件将在迭代4实现
3. **基础目标准备**: 当前实现基础版本的目标准备，完整版本在迭代5
4. **验证驱动**: 每个操作都必须通过验证才能继续
5. **错误处理**: 增强了错误处理机制，提高训练稳定性

## 📝 执行检查清单

- [ ] 实现基础目标准备逻辑（`lore_tsr_target_preparation.py`）
- [ ] 增强数据集目标准备集成（`lore_tsr_dataset.py`）
- [ ] 完善数据预处理（`lore_tsr_transforms.py`）
- [ ] 添加验证循环和指标记录（`train_lore_tsr.py`）
- [ ] 完善模型保存逻辑（`train_lore_tsr.py`）
- [ ] 执行验证命令1：目标准备逻辑测试
- [ ] 执行验证命令2：增强数据集测试
- [ ] 执行验证命令3：验证循环测试
- [ ] 执行验证命令4：完整功能集成测试
- [ ] 确认所有验证通过，训练循环更加健壮
- [ ] 生成步骤3.2执行报告
