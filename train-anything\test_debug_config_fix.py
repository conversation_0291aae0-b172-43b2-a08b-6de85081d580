#!/usr/bin/env python3
"""
验证debug配置修正的测试脚本
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from omegaconf import OmegaConf
from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset

def test_config_debug_fix():
    """测试debug配置修正"""
    print("=" * 60)
    print("测试: debug配置修正验证")
    print("=" * 60)
    
    # 加载配置文件
    config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
    print('✅ 配置文件加载成功')
    
    # 验证basic.debug存在
    assert 'debug' in config.basic, "basic配置中应该有debug字段"
    print(f'✅ basic.debug存在: {config.basic.debug}')
    
    # 验证data.processing中没有debug
    assert 'debug' not in config.data.processing, "data.processing中不应该有debug字段"
    print('✅ data.processing中没有重复的debug字段')
    
    # 验证其他关键配置
    print(f'✅ 训练数据目录: {config.data.paths.train_data_dir}')
    print(f'✅ 最大样本数: {config.data.processing.max_samples}')
    print(f'✅ 图像尺寸: {config.data.processing.image_size}')
    
    return True

def test_dataset_debug_usage():
    """测试数据集debug使用"""
    print("\n" + "=" * 60)
    print("测试: 数据集debug使用验证")
    print("=" * 60)
    
    # 加载配置
    config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
    
    # 测试debug=0的情况
    config.basic.debug = 0
    dataset = LoreTsrDataset(config, mode='train')
    print(f'✅ debug=0时数据集创建成功: {len(dataset)} 个样本')
    print(f'✅ 数据集debug状态: {dataset.debug}')
    
    # 测试debug=1的情况
    config.basic.debug = 1
    dataset_debug = LoreTsrDataset(config, mode='train')
    print(f'✅ debug=1时数据集创建成功: {len(dataset_debug)} 个样本')
    print(f'✅ 数据集debug状态: {dataset_debug.debug}')
    
    return True

def main():
    """主测试函数"""
    print("debug配置修正验证测试")
    print("目标: 验证debug字段不重复，作用链路正确")
    
    try:
        # 执行测试
        test_config_debug_fix()
        test_dataset_debug_usage()
        
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)
        print("🎉 所有测试通过！debug配置修正成功")
        print("✅ 移除了重复的debug字段")
        print("✅ 作用链路验证正确")
        print("✅ 数据集正常工作")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
