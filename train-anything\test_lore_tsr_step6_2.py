#!/usr/bin/env python3
"""
LORE-TSR 迁移验证测试 - 步骤6.2：Processor组件核心实现

验证内容：
1. Processor组件导入和实例化测试
2. Processor功能验证测试
3. 训练循环集成验证
4. 与Transformer组件集成测试
5. 项目完整性验证

Time: 2025-07-20
Author: LORE-TSR Migration Team
"""

import sys
import torch
import traceback
from omegaconf import OmegaConf

def test_processor_import():
    """测试Processor组件导入和实例化"""
    print("=" * 60)
    print("测试1: Processor组件导入和实例化测试")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.processor import Processor, Stacker
        from networks.lore_tsr.transformer import Transformer
        from omegaconf import OmegaConf
        
        print("✅ Processor组件导入成功")
        
        # 加载配置
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        print("✅ 配置文件加载成功")
        
        # 实例化Processor
        processor = Processor(config)
        param_count = sum(p.numel() for p in processor.parameters())
        print(f"✅ Processor实例化成功: {param_count:,} 个参数")
        
        # 验证配置参数
        print(f"  - wiz_stacking: {config.processor.wiz_stacking}")
        print(f"  - wiz_2dpe: {config.processor.wiz_2dpe}")
        print(f"  - wiz_4ps: {config.processor.wiz_4ps}")
        print(f"  - wiz_vanilla: {config.processor.wiz_vanilla}")
        print(f"  - Transformer层数: {config.processor.tsfm_layers}")
        print(f"  - 隐藏层大小: {config.processor.hidden_size}")
        
        return True
    except Exception as e:
        print(f"❌ Processor组件导入失败: {e}")
        traceback.print_exc()
        return False

def test_processor_functionality():
    """测试Processor功能"""
    print("\n" + "=" * 60)
    print("测试2: Processor功能验证测试")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.processor import Processor
        from omegaconf import OmegaConf
        
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        processor = Processor(config)
        
        # 测试推理模式（无batch）
        batch_size, seq_len, feature_dim = 2, 100, 256
        dummy_outputs = torch.randn(batch_size, seq_len, feature_dim)
        
        print(f"输入特征形状: {dummy_outputs.shape}")
        
        # 推理模式测试
        logic_axis = processor(dummy_outputs, batch=None)
        if config.processor.wiz_stacking:
            logic_axis, stacked_axis = logic_axis
            print(f"✅ 推理模式（堆叠）: 输入{dummy_outputs.shape} -> 逻辑轴向{logic_axis.shape}, 堆叠轴向{stacked_axis.shape}")
        else:
            print(f"✅ 推理模式（基础）: 输入{dummy_outputs.shape} -> 逻辑轴向{logic_axis.shape}")
        
        # 测试get_logic_axis方法
        logic_axis_method = processor.get_logic_axis(dummy_outputs, batch=None)
        print(f"✅ get_logic_axis方法: 输出{logic_axis_method.shape}")
        
        # 测试get_stacked_logic_axis方法
        if config.processor.wiz_stacking:
            stacked_axis_method = processor.get_stacked_logic_axis(dummy_outputs, batch=None)
            print(f"✅ get_stacked_logic_axis方法: 输出{stacked_axis_method.shape}")
        else:
            stacked_axis_method = processor.get_stacked_logic_axis(dummy_outputs, batch=None)
            print(f"✅ get_stacked_logic_axis方法: 输出None（wiz_stacking=False）")
        
        return True
    except Exception as e:
        print(f"❌ Processor功能验证失败: {e}")
        traceback.print_exc()
        return False

def test_training_loop_integration():
    """测试训练循环集成"""
    print("\n" + "=" * 60)
    print("测试3: 训练循环集成验证")
    print("=" * 60)
    
    try:
        from training_loops.table_structure_recognition.train_lore_tsr import setup_training_components
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        from omegaconf import OmegaConf
        from accelerate import Accelerator
        
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        accelerator = Accelerator()
        model = create_lore_tsr_model(config)
        
        print("✅ 模型创建成功")
        
        # 设置训练组件
        loss_criterion, processor, optimizer, lr_scheduler, max_train_steps, train_datasets, train_loaders, val_loaders, seed = \
            setup_training_components(config, model, None, None, accelerator)
        
        print("✅ 训练组件设置成功")
        print(f"✅ Processor类型: {type(processor).__name__}")
        print(f"✅ Processor设备: {processor.device}")
        
        # 验证Processor是真实的Processor而不是DummyProcessor
        from networks.lore_tsr.processor import Processor
        assert isinstance(processor, Processor), f"期望Processor类型，实际得到{type(processor)}"
        print("✅ 确认使用真实Processor组件")
        
        return True
    except Exception as e:
        print(f"❌ 训练循环集成验证失败: {e}")
        traceback.print_exc()
        return False

def test_transformer_integration():
    """测试与Transformer组件集成"""
    print("\n" + "=" * 60)
    print("测试4: 与Transformer组件集成测试")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.processor import Processor
        from networks.lore_tsr.transformer import Transformer
        from omegaconf import OmegaConf
        
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        processor = Processor(config)
        
        # 验证Processor内部的Transformer组件
        assert hasattr(processor, 'tsfm_axis'), "Processor应该包含tsfm_axis属性"
        assert isinstance(processor.tsfm_axis, Transformer), "tsfm_axis应该是Transformer实例"
        
        print("✅ Processor包含Transformer组件")
        print(f"  - Transformer输入维度: {processor.tsfm_axis.linear.in_features}")
        print(f"  - Transformer隐藏维度: {processor.tsfm_axis.linear.out_features}")
        print(f"  - Encoder层数: {processor.tsfm_axis.encoder.N}")
        
        # 如果启用堆叠，验证Stacker组件
        if config.processor.wiz_stacking:
            assert hasattr(processor, 'stacker'), "启用wiz_stacking时应该包含stacker属性"
            print("✅ Processor包含Stacker组件")
            print(f"  - Stacker Transformer输入维度: {processor.stacker.tsfm.linear.in_features}")
        
        # 验证位置嵌入
        assert hasattr(processor, 'x_position_embeddings'), "Processor应该包含x_position_embeddings"
        assert hasattr(processor, 'y_position_embeddings'), "Processor应该包含y_position_embeddings"
        print("✅ Processor包含位置嵌入组件")
        
        return True
    except Exception as e:
        print(f"❌ Transformer组件集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_project_integrity():
    """测试项目完整性"""
    print("\n" + "=" * 60)
    print("测试5: 项目完整性验证")
    print("=" * 60)
    
    try:
        # 测试所有组件仍然可用
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
        from networks.lore_tsr.transformer import Transformer
        from networks.lore_tsr.processor import Processor
        from networks.lore_tsr.processor_utils import _tranpose_and_gather_feat
        from omegaconf import OmegaConf
        
        print("✅ 所有组件导入成功")
        
        # 测试通过networks.lore_tsr导入
        from networks.lore_tsr import (
            Processor, Stacker, Transformer, 
            _tranpose_and_gather_feat, _get_4ps_feat
        )
        print("✅ 通过networks.lore_tsr统一导入成功")
        
        # 验证配置文件兼容性
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        print("✅ 配置文件加载成功")
        
        # 验证所有组件可以正常实例化
        model = create_lore_tsr_model(config)
        loss_fn = LoreTsrLoss(config)
        processor = Processor(config)
        transformer = Transformer(256, 256, 4, 6, 8, 0.1)
        
        print("✅ 所有组件实例化成功")
        print("✅ 项目保持完全可运行状态")
        
        return True
    except Exception as e:
        print(f"❌ 项目完整性验证失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始LORE-TSR步骤6.2验证测试")
    print("测试目标：Processor组件核心实现")
    
    test_results = []
    
    # 执行所有测试
    test_results.append(("Processor组件导入和实例化", test_processor_import()))
    test_results.append(("Processor功能验证", test_processor_functionality()))
    test_results.append(("训练循环集成验证", test_training_loop_integration()))
    test_results.append(("Transformer组件集成", test_transformer_integration()))
    test_results.append(("项目完整性验证", test_project_integrity()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 步骤6.2验证测试全部通过！")
        print("✅ Processor组件核心实现成功")
        print("✅ 成功集成到训练循环，替换DummyProcessor")
        print("✅ 项目保持可运行状态")
        print("✅ 为迭代6后续步骤提供完整的逻辑结构恢复功能")
        return True
    else:
        print("❌ 部分测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
