#!/usr/bin/env python3
"""
LORE-TSR 虚拟数据生成工具

用于迭代3测试训练循环，生成符合train-anything框架TableDataset格式的虚拟数据
支持WTW标注格式和分布式目录组织
"""

import os
import json
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tu<PERSON>


def create_dummy_part_structure(base_dir: str, num_parts: int = 1, samples_per_part: int = 10):
    """
    创建符合train-anything数据集组织的虚拟part目录结构
    
    Args:
        base_dir: 基础目录路径
        num_parts: part目录数量
        samples_per_part: 每个part的样本数量
        
    Returns:
        List[str]: 创建的part目录路径列表
    """
    part_dirs = []
    
    for part_idx in range(num_parts):
        part_name = f"part_{part_idx+1:04d}"
        part_dir = os.path.join(base_dir, part_name)
        os.makedirs(part_dir, exist_ok=True)
        
        # 生成虚拟样本
        for sample_idx in range(samples_per_part):
            # 创建虚拟图像文件（真实的图像数据）
            image_name = f"image_{sample_idx+1:03d}.jpg"
            image_path = os.path.join(part_dir, image_name)

            # 创建一个简单的虚拟图像
            import cv2
            dummy_image = np.ones((768, 768, 3), dtype=np.uint8) * 255  # 白色图像
            cv2.imwrite(image_path, dummy_image)
            
            # 创建对应的JSON标注
            json_name = f"image_{sample_idx+1:03d}.json"
            json_path = os.path.join(part_dir, json_name)
            
            # 生成WTW格式标注
            annotation = generate_wtw_format_annotation((768, 768))
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(annotation, f, ensure_ascii=False, indent=2)
        
        part_dirs.append(part_dir)
    
    return part_dirs


def generate_wtw_format_annotation(image_size: Tuple[int, int]) -> Dict:
    """
    生成符合WTW格式的单个样本标注数据
    
    Args:
        image_size: 图像尺寸 (width, height)
        
    Returns:
        Dict: WTW格式的标注数据
    """
    width, height = image_size
    
    # 生成虚拟表格结构（3x3表格）
    rows, cols = 3, 3
    cell_width = width // cols
    cell_height = height // rows
    
    cells = []
    
    for row in range(rows):
        for col in range(cols):
            # 计算单元格边界框
            x1 = col * cell_width
            y1 = row * cell_height
            x2 = x1 + cell_width
            y2 = y1 + cell_height
            
            # 添加一些随机偏移
            x1 += random.randint(-5, 5)
            y1 += random.randint(-5, 5)
            x2 += random.randint(-5, 5)
            y2 += random.randint(-5, 5)
            
            # 确保边界在图像范围内
            x1 = max(0, min(x1, width-1))
            y1 = max(0, min(y1, height-1))
            x2 = max(x1+1, min(x2, width))
            y2 = max(y1+1, min(y2, height))
            
            cell = {
                "bbox": {
                    "p1": [x1, y1],
                    "p2": [x2, y1],
                    "p3": [x2, y2],
                    "p4": [x1, y2]
                },
                "lloc": {
                    "start_row": row,
                    "end_row": row,
                    "start_col": col,
                    "end_col": col
                }
            }
            cells.append(cell)
    
    # WTW格式标注
    annotation = {
        "cells": cells,
        "quality": "合格"  # 只加载"合格"样本
    }
    
    return annotation


def create_quality_valid_sample():
    """创建质量合格的样本数据"""
    return generate_wtw_format_annotation((768, 768))
