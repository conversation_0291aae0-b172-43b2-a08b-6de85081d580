#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 10:00
# <AUTHOR> <EMAIL>
# @FileName: __init__.py

"""
表格结构识别数据集模块

该模块包含表格结构识别任务的数据集处理功能，包括：
- table_dataset.py: 表格数据集基类，处理JSON标注
- table_transforms.py: 数据预处理和增强管道
"""

# 导入数据集和变换模块
from .table_dataset import TableDataset
from .table_transforms import TableTransforms, collate_fn

# 导入LORE-TSR数据集模块（迭代5.2）
from .lore_tsr_dataset import LoreTsrDataset

# 导入目标准备模块
from .target_preparation import (
    create_gaussian_heatmap_target,
    create_offset_target,
    create_center2vertex_target,
    create_vertex2center_target,
    prepare_targets,
    TargetGenerator,
    create_full_target_generator
)

__all__ = [
    # 数据集和变换
    'TableDataset',
    'TableTransforms',
    'collate_fn',

    # LORE-TSR数据集（迭代5.2）
    'LoreTsrDataset',

    # 目标准备
    'create_gaussian_heatmap_target',
    'create_offset_target',
    'create_center2vertex_target',
    'create_vertex2center_target',
    'prepare_targets',
    'TargetGenerator',
    'create_full_target_generator'
]
