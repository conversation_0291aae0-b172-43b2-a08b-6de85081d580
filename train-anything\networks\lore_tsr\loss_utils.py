#!/usr/bin/env python3
"""
LORE-TSR 损失函数辅助工具模块

从原LORE-TSR的utils.py复制核心辅助函数，保持算法逻辑不变
严格遵循"复制保留核心算法"原则，逐行复制原始实现

基于：LORE-TSR/src/lib/models/utils.py
"""

import torch


def _gather_feat(feat, ind, mask=None):
    """
    特征收集函数 - 从LORE-TSR逐行复制
    
    根据索引从特征图中收集特征
    
    Args:
        feat: 特征张量 (batch, dim, ...)
        ind: 索引张量 (batch, max_objects)
        mask: 可选掩码张量
        
    Returns:
        收集的特征张量
    """
    dim = feat.size(2)
    ind = ind.unsqueeze(2).expand(ind.size(0), ind.size(1), dim)
    feat = feat.gather(1, ind)
    if mask is not None:
        mask = mask.unsqueeze(2).expand_as(feat)
        feat = feat[mask]
        feat = feat.view(-1, dim)
    return feat


def _tranpose_and_gather_feat(feat, ind):
    """
    转置并收集特征 - LORE-TSR核心函数
    
    转置特征图并根据索引收集特征，LORE-TSR损失函数的核心辅助函数
    
    Args:
        feat: 特征张量 (batch, channels, height, width)
        ind: 索引张量 (batch, max_objects)
        
    Returns:
        收集的特征张量 (batch, max_objects, channels)
    """
    feat = feat.permute(0, 2, 3, 1).contiguous()
    feat = feat.view(feat.size(0), -1, feat.size(3))
    feat = _gather_feat(feat, ind)
    return feat


def _flatten_and_gather_feat(output, ind):
    """
    展平并收集特征 - 从LORE-TSR复制
    
    Args:
        output: 输出张量
        ind: 索引张量
        
    Returns:
        收集的特征张量
    """
    dim = output.size(3)
    ind = ind.unsqueeze(2).expand(ind.size(0), ind.size(1), dim)
    output = output.contiguous().view(output.size(0), -1, output.size(3))
    output1 = output.gather(1, ind)
    return output1
