# LORE-TSR 迁移详细设计文档 - 迭代6：Processor组件集成

## 项目结构与总体设计

### 迭代6目标
实现LORE-TSR的核心Processor和Transformer组件，完成逻辑结构恢复功能的集成。这是LORE-TSR算法的核心组件，负责将模型检测输出转换为逻辑表格结构信息。

### 核心设计原则
1. **复制保留策略**：直接从LORE-TSR复制Processor和Transformer的核心算法逻辑，确保数值计算完全一致
2. **接口适配策略**：调整接口以适配train-anything框架，替换现有的DummyProcessor
3. **模块化设计**：将Processor和Transformer分离为独立模块，便于维护和扩展
4. **配置驱动**：充分利用现有配置系统，支持LORE-TSR的各种特性开关

### 架构概述
迭代6将实现完整的逻辑结构恢复pipeline：
```
模型输出 → 特征提取 → 位置嵌入 → Transformer处理 → 逻辑轴向输出 → AxisLoss计算
```

## 目录结构树

```
train-anything/
├── networks/lore_tsr/
│   ├── __init__.py                    # 更新：导出新组件
│   ├── processor.py                   # 新增：真实Processor实现
│   ├── transformer.py                 # 新增：Transformer组件实现
│   ├── lore_tsr_model.py             # 现有：模型定义
│   ├── lore_tsr_loss.py              # 现有：损失函数
│   └── ...
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py             # 修改：集成真实Processor
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml          # 现有：配置文件（已预留processor配置）
├── modules/utils/lore_tsr/
│   ├── dummy_processor.py            # 现有：保留作为备用
│   └── __init__.py                   # 更新：导出新组件
└── test_lore_tsr_iter6.py            # 新增：迭代6验证测试
```

## 整体逻辑和交互时序图

```mermaid
sequenceDiagram
    participant TL as TrainingLoop
    participant M as LoreTsrModel
    participant P as Processor
    participant T as Transformer
    participant L as AxisLoss
    
    TL->>M: forward(images)
    M->>TL: model_outputs
    
    Note over TL: 训练模式
    TL->>P: forward(outputs, batch)
    P->>P: extract_features(outputs)
    P->>P: apply_position_embeddings()
    P->>T: forward(features, mask)
    T->>P: logic_embeddings
    P->>TL: logic_axis, stacked_axis
    
    TL->>L: forward(outputs, logic_axis)
    L->>TL: axis_loss
    
    Note over TL: 推理模式
    TL->>P: forward(outputs, batch=None)
    P->>T: forward(features, mask=None)
    T->>P: logic_embeddings
    P->>TL: logic_axis
```

## 数据实体结构深化

```mermaid
erDiagram
    ModelOutputs {
        tensor hm "热力图输出 [B, C, H, W]"
        tensor wh "宽高回归 [B, 8, H, W]"
        tensor reg "偏移回归 [B, 2, H, W]"
        tensor ax "轴向回归 [B, 4, H, W]"
    }
    
    ProcessorInputs {
        dict outputs "模型输出字典"
        dict batch "批次数据（可选）"
        tensor dets "检测结果（可选）"
        tensor cc_match "单元格匹配（可选）"
    }
    
    ProcessorOutputs {
        tensor logic_axis "逻辑轴向 [B, K, 4]"
        tensor stacked_axis "堆叠轴向 [B, K, 4]（可选）"
    }
    
    TransformerData {
        tensor input_features "输入特征 [B, K, input_size]"
        tensor position_embeddings "位置嵌入 [B, K, hidden_size]"
        tensor attention_mask "注意力掩码 [B, K]（可选）"
        tensor output_embeddings "输出嵌入 [B, K, output_size]"
    }
    
    ModelOutputs ||--|| ProcessorInputs : "作为输入"
    ProcessorInputs ||--|| TransformerData : "特征提取"
    TransformerData ||--|| ProcessorOutputs : "逻辑恢复"
```

## 配置项

基于现有的`lore_tsr_config.yaml`中的processor配置节：

```yaml
processor:
  # 核心功能开关
  wiz_2dpe: false          # 启用2D位置嵌入
  wiz_4ps: false           # 启用四角点特征
  wiz_stacking: false      # 启用堆叠回归器
  wiz_vanilla: true        # 使用基础版本
  
  # Transformer配置
  tsfm_layers: 6           # Transformer层数
  hidden_size: 256         # 隐藏层大小
  input_size: 256          # 输入特征大小
  output_size: 4           # 输出大小（四个逻辑索引）
  num_heads: 8             # 多头注意力头数
  att_dropout: 0.1         # 注意力dropout率
  
  # 位置嵌入配置
  max_fmp_size: 256        # 最大特征图大小
  
  # 检测配置
  K: 100                   # 最大检测数量
  MK: 700                  # 最大关键点数量
  
  # 堆叠配置（当wiz_stacking=true时）
  stacking_layers: 3       # 堆叠层数
```

## 模块化文件详解

### networks/lore_tsr/transformer.py

#### 文件用途说明
实现LORE-TSR的Transformer组件，包含完整的Encoder-Decoder架构。直接从LORE-TSR的transformer.py复制核心算法，确保数值计算完全一致。

#### 文件内类图
```mermaid
classDiagram
    class MultiHeadAttention {
        +d_model: int
        +heads: int
        +dropout: float
        +__init__(heads, d_model, dropout)
        +forward(q, k, v, mask) Tensor
    }
    
    class FeedForward {
        +d_model: int
        +d_ff: int
        +dropout: float
        +__init__(d_model, d_ff, dropout)
        +forward(x) Tensor
    }
    
    class EncoderLayer {
        +attention: MultiHeadAttention
        +feed_forward: FeedForward
        +norm1: LayerNorm
        +norm2: LayerNorm
        +__init__(d_model, heads, dropout)
        +forward(x, mask) Tensor
    }
    
    class Encoder {
        +layers: ModuleList
        +norm: LayerNorm
        +__init__(input_size, hidden_size, n_layers, heads, dropout)
        +forward(x, mask, require_att) Tensor
    }
    
    class Decoder {
        +linear: Linear
        +__init__(hidden_size, output_size)
        +forward(x) Tensor
    }
    
    class Transformer {
        +linear: Linear
        +encoder: Encoder
        +decoder: Decoder
        +__init__(input_size, hidden_size, output_size, n_layers, heads, dropout)
        +forward(x, mask, require_att) Tensor
    }
    
    Transformer --> Encoder
    Transformer --> Decoder
    Encoder --> EncoderLayer
    EncoderLayer --> MultiHeadAttention
    EncoderLayer --> FeedForward
```

#### 函数/方法详解

##### Transformer.__init__
- **用途**: 初始化Transformer组件
- **输入参数**:
  - `input_size`: 输入特征维度
  - `hidden_size`: 隐藏层维度
  - `output_size`: 输出维度
  - `n_layers`: Encoder层数
  - `heads`: 注意力头数
  - `dropout`: Dropout率
- **输出数据结构**: 初始化完成的Transformer实例
- **实现流程**:
```mermaid
flowchart TD
    A[创建输入线性层] --> B[创建Encoder]
    B --> C[创建Decoder]
    C --> D[初始化完成]
```

##### Transformer.forward
- **用途**: Transformer前向传播
- **输入参数**:
  - `x`: 输入特征张量 [B, K, input_size]
  - `mask`: 注意力掩码（可选）
  - `require_att`: 是否返回注意力权重
- **输出数据结构**: 
  - 基础模式: 输出张量 [B, K, output_size]
  - 注意力模式: (输出张量, 注意力权重)
- **实现流程**:
```mermaid
flowchart TD
    A[输入线性变换] --> B{是否有mask?}
    B -->|有| C[训练模式Encoder]
    B -->|无| D[推理模式Encoder]
    C --> E[Decoder解码]
    D --> E
    E --> F{require_att?}
    F -->|是| G[返回输出+注意力]
    F -->|否| H[返回输出]
```

### networks/lore_tsr/processor.py

#### 文件用途说明
实现LORE-TSR的核心Processor组件，负责特征提取、位置嵌入和逻辑结构恢复。直接从LORE-TSR的classifier.py复制核心算法。

#### 文件内类图
```mermaid
classDiagram
    class Stacker {
        +tsfm: Transformer
        +logi_encoder: Linear
        +__init__(input_size, hidden_size, output_size, layers)
        +forward(outputs, logi, mask, require_att) Tensor
    }
    
    class Processor {
        +config: DictConfig
        +tsfm_axis: Transformer
        +x_position_embeddings: Embedding
        +y_position_embeddings: Embedding
        +stacker: Stacker
        +device: torch.device
        +__init__(config)
        +forward(outputs, batch, cc_match, dets) Tensor
        +_extract_features(outputs, batch) Tensor
        +_apply_position_embeddings(feat, batch) Tensor
        +get_logic_axis(outputs, batch) Tensor
        +get_stacked_logic_axis(outputs, batch) Tensor
        +to(device) Processor
    }
    
    Processor --> Transformer
    Processor --> Stacker
    Stacker --> Transformer
```

#### 函数/方法详解

##### Processor.__init__
- **用途**: 初始化Processor组件
- **输入参数**:
  - `config`: OmegaConf配置对象
- **输出数据结构**: 初始化完成的Processor实例
- **实现流程**:
```mermaid
flowchart TD
    A[解析配置参数] --> B{wiz_stacking?}
    B -->|是| C[创建Stacker组件]
    B -->|否| D[创建Transformer组件]
    C --> D
    D --> E[创建位置嵌入层]
    E --> F[保存配置对象]
```

##### Processor.forward
- **用途**: Processor主要前向传播逻辑
- **输入参数**:
  - `outputs`: 模型输出字典
  - `batch`: 批次数据（训练模式）
  - `cc_match`: 单元格匹配信息（可选）
  - `dets`: 检测结果（可选）
- **输出数据结构**:
  - 基础模式: logic_axis张量 [B, K, 4]
  - 堆叠模式: (logic_axis, stacked_axis)
- **实现流程**:
```mermaid
sequenceDiagram
    participant P as Processor
    participant FE as FeatureExtractor
    participant PE as PositionEmbedding
    participant T as Transformer
    participant S as Stacker

    P->>FE: extract_features(outputs, batch)
    FE->>P: ct_feat
    P->>PE: apply_position_embeddings(ct_feat)
    PE->>P: feat_with_pos

    alt 训练模式
        P->>T: forward(feat, mask=mask)
    else 推理模式
        P->>T: forward(feat, mask=None)
    end
    T->>P: logic_axis

    alt wiz_stacking启用
        P->>S: forward(feat, logic_axis, mask)
        S->>P: stacked_axis
        P->>P: return (logic_axis, stacked_axis)
    else 基础模式
        P->>P: return logic_axis
    end
```

##### Processor._extract_features
- **用途**: 从模型输出中提取特征
- **输入参数**:
  - `outputs`: 模型输出字典
  - `batch`: 批次数据
- **输出数据结构**: 特征张量 [B, K, input_size]
- **实现流程**:
```mermaid
flowchart TD
    A[获取检测结果] --> B{wiz_2dpe?}
    B -->|是| C[提取2D位置特征]
    B -->|否| D[提取基础特征]
    C --> E{wiz_4ps?}
    D --> E
    E -->|是| F[添加四角点特征]
    E -->|否| G[使用基础特征]
    F --> H[特征融合]
    G --> H
    H --> I[返回融合特征]
```

##### Processor._apply_position_embeddings
- **用途**: 应用位置嵌入到特征
- **输入参数**:
  - `feat`: 输入特征张量
  - `batch`: 批次数据
- **输出数据结构**: 带位置嵌入的特征张量
- **实现流程**:
```mermaid
flowchart TD
    A[计算x位置索引] --> B[计算y位置索引]
    B --> C[获取x位置嵌入]
    C --> D[获取y位置嵌入]
    D --> E[特征+位置嵌入融合]
    E --> F[返回融合特征]
```

### training_loops/table_structure_recognition/train_lore_tsr.py

#### 修改说明
更新训练循环以集成真实的Processor组件，替换DummyProcessor。

#### 主要修改点

##### setup_training_components函数修改
- **修改内容**: 创建真实Processor替换DummyProcessor
- **修改代码**:
```python
# 原代码
processor = DummyProcessor(config)

# 修改为
from networks.lore_tsr.processor import Processor
processor = Processor(config)
```

##### 训练循环中的Processor调用
- **修改位置**: run_training_loop函数中的前向传播部分
- **修改内容**: 在模型前向传播后调用Processor
- **实现流程**:
```mermaid
sequenceDiagram
    participant TL as TrainingLoop
    participant M as Model
    participant P as Processor
    participant L as LossFunction

    TL->>M: forward(images)
    M->>TL: outputs

    Note over TL: 新增Processor调用
    alt 训练模式
        TL->>P: forward(outputs, batch)
    else 推理模式
        TL->>P: forward(outputs, batch=None)
    end
    P->>TL: logic_axis, stacked_axis

    TL->>L: forward(outputs, batch, logic_axis)
    L->>TL: total_loss
```

### networks/lore_tsr/__init__.py

#### 修改说明
更新模块导出，添加新的Processor和Transformer组件。

#### 修改内容
```python
# 新增导出
from .processor import Processor
from .transformer import Transformer

__all__ = [
    "create_lore_tsr_model",
    "LoreTsrLoss",
    "Processor",           # 新增
    "Transformer",         # 新增
    # ... 其他现有导出
]
```

## 迭代演进依据

### 当前迭代6的设计优势
1. **模块化架构**: Processor和Transformer分离，便于独立维护和测试
2. **配置驱动**: 充分利用现有配置系统，支持灵活的功能开关
3. **接口标准化**: 与现有DummyProcessor接口完全兼容，无缝替换
4. **算法保真**: 直接复制LORE-TSR核心算法，确保数值一致性

### 后续迭代扩展路径
1. **迭代7**: 外部依赖集成（DCNv2、NMS等）
2. **迭代8**: 权重兼容性实现
3. **迭代9**: 可视化功能扩展
4. **迭代10**: 端到端验证

### 扩展预留接口
1. **Stacker组件**: 当前实现基础版本，后续可扩展更复杂的堆叠逻辑
2. **特征融合**: 预留多种特征融合策略的扩展点
3. **注意力机制**: 支持更高级的注意力可视化和分析
4. **性能优化**: 预留批处理优化和推理加速的接口

## 如何迁移 LORE-TSR Processor组件

### 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 修改说明 |
|-------------------|---------------------------|----------|----------|
| `lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留 | 调整import路径，适配配置系统 |
| `lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留 | 调整import路径，保持算法不变 |
| `main.py` (Processor调用部分) | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配 | 集成到accelerate训练循环 |

### 关键适配点

#### 1. 配置系统适配
```python
# LORE-TSR原配置方式
opt.wiz_stacking
opt.hidden_size
opt.tsfm_layers

# train-anything配置方式
config.processor.wiz_stacking
config.processor.hidden_size
config.processor.tsfm_layers
```

#### 2. 设备管理适配
```python
# LORE-TSR原方式
model.cuda()

# train-anything方式
model.to(accelerator.device)
```

#### 3. 训练循环集成
```python
# LORE-TSR原调用方式
if self.opt.wiz_stacking:
    _, slct_logi = self.processor(slct_logi, dets=slct_dets)
else:
    slct_logi = self.processor(slct_logi, dets=slct_dets)

# train-anything集成方式
if config.processor.wiz_stacking:
    logic_axis, stacked_axis = processor(outputs, batch)
else:
    logic_axis = processor(outputs, batch)
```

### 验证策略

#### 数值一致性验证
1. **特征提取验证**: 确保_extract_features输出与原LORE-TSR一致
2. **Transformer验证**: 验证注意力计算和编码结果
3. **逻辑轴向验证**: 确保最终logic_axis输出数值正确
4. **损失计算验证**: 验证AxisLoss计算结果一致

#### 功能完整性验证
1. **训练模式**: 验证带mask的训练流程
2. **推理模式**: 验证无mask的推理流程
3. **堆叠模式**: 验证wiz_stacking功能
4. **配置开关**: 验证各种wiz_*配置的效果

---

**文档版本**: v1.0
**创建日期**: 2025-07-20
**迭代范围**: 迭代6 - Processor组件集成
**依赖迭代**: 迭代1-5（已完成）
**后续迭代**: 迭代7-11（外部依赖、权重兼容性、验证等）
