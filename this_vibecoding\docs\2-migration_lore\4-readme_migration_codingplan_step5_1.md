# LORE-TSR 迁移项目 - 迭代5步骤5.1渐进式小步迁移计划

## 📋 文档信息
- **迁移阶段**: 迭代5 - 数据集适配器实现
- **当前步骤**: 步骤5.1 - 基础工具函数迁移（最小可验证单元）
- **制定日期**: 2025-07-20
- **基于文档**: 
  - PRD: @`this_vibecoding/docs/2-migration_lore/2-readme_migration_lore_prdplan.md`
  - LLD: @`this_vibecoding/docs/2-migration_lore/3-readme_migration_lore_lld_iter5.md`

## 🎯 迭代5.1核心目标

### 总体目标
建立LORE-TSR基础工具函数，确保核心算法正确性，为后续数据处理pipeline提供可靠的算法基础。

### 核心原则
- **复制并保留核心算法**: 严格遵循三分法迁移原则，逐行复制LORE-TSR/src/lib/utils/image.py
- **数值精度一致**: 确保所有算法函数与原项目数值完全一致
- **最小可验证单元**: 每个小步都可独立验证，确保项目可运行状态

## 📊 动态迁移蓝图

### 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/utils/image.py` | `modules/utils/lore_tsr/lore_image_utils.py` | **复制保留：逐行复制核心算法** | **迭代5.1** | 简单 | **进行中** |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配：数据集适配器 | 迭代5.2 | **复杂** | `未开始` |
| `src/lib/datasets/sample/ctdet.py` | `my_datasets/table_structure_recognition/lore_tsr_transforms.py` | 复制保留：完整数据处理pipeline | 迭代5.3 | **复杂** | `未开始` |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | `已完成` |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | `已完成` |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留：模型工厂函数 | 迭代2 | **复杂** | `已完成` |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | 简单 | `已完成` |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |

### 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代5.1 - 基础工具函数迁移

    subgraph "Source: LORE-TSR/src/lib/utils/image.py"
        direction LR
        S1["get_affine_transform()"]
        S2["get_affine_transform_upper_left()"]
        S3["affine_transform()"]
        S4["gaussian_radius()"]
        S5["draw_umich_gaussian()"]
        S6["color_aug()"]
        S7["其他辅助函数"]
    end

    subgraph "Target: train-anything/modules/utils/lore_tsr/"
        direction LR
        T1["lore_image_utils.py"]
        T2["__init__.py"]
        T3["单元测试模块"]
    end

    %% 迁移映射 - 复制保留策略
    S1 -- "Copy & Preserve" --> T1
    S2 -- "Copy & Preserve" --> T1
    S3 -- "Copy & Preserve" --> T1
    S4 -- "Copy & Preserve" --> T1
    S5 -- "Copy & Preserve" --> T1
    S6 -- "Copy & Preserve" --> T1
    S7 -- "Copy & Preserve" --> T1

    %% 依赖关系
    T2 -.-> T1
    T1 -.-> T3

    %% 验证流程
    T3 -.-> V1["数值精度验证"]
    T3 -.-> V2["功能完整性验证"]
    T3 -.-> V3["与原项目对比验证"]
```

## 🏗️ 目标目录结构树

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                      # [已完成]
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                         # [已完成]
├── networks/lore_tsr/
│   ├── __init__.py                               # [已完成]
│   ├── lore_tsr_model.py                         # [已完成]
│   ├── lore_tsr_loss.py                          # [已完成]
│   ├── backbones/                                # [已完成]
│   └── heads/                                    # [已完成]
├── my_datasets/table_structure_recognition/      # [待创建]
│   ├── lore_tsr_dataset.py                       # [待创建]
│   ├── lore_tsr_transforms.py                    # [待创建]
│   └── lore_tsr_target_preparation.py            # [待创建]
├── modules/utils/lore_tsr/                       # [当前创建]
│   ├── __init__.py                               # [步骤5.1.1创建]
│   └── lore_image_utils.py                       # [步骤5.1.2-5.1.3创建]
└── external/lore_tsr/                            # [待创建]
    ├── DCNv2/                                    # [待创建]
    ├── NMS/                                      # [待创建]
    └── cocoapi/                                  # [待创建]
```

## 🔄 渐进式小步迁移计划

### 步骤5.1.1: 创建基础目录结构和模块初始化

**当前迭代**: 迭代5.1 - 基础工具函数迁移  
**步骤目标**: 建立LORE-TSR工具函数的基础目录结构，确保模块可正确导入

**影响文件**:
- 创建: `modules/utils/lore_tsr/__init__.py`
- 创建: `modules/utils/lore_tsr/` 目录

**具体操作**:
1. 创建目录结构
2. 初始化Python模块
3. 设置基础的模块导入接口

**受影响的现有模块**: 无，纯增量添加

**复用已有代码**: 参考train-anything现有的modules/utils目录结构

**如何验证**:
```bash
# 验证目录创建成功
ls -la modules/utils/lore_tsr/

# 验证模块可正确导入
cd train-anything
python -c "from modules.utils.lore_tsr import *; print('LORE-TSR工具模块导入成功')"
```

### 步骤5.1.2: 实现仿射变换核心函数

**当前迭代**: 迭代5.1 - 基础工具函数迁移  
**步骤目标**: 完整迁移LORE-TSR的仿射变换相关函数，确保数值精度一致

**影响文件**:
- 创建: `modules/utils/lore_tsr/lore_image_utils.py` (仿射变换部分)

**具体操作**:
1. 从LORE-TSR/src/lib/utils/image.py逐行复制以下函数:
   - `get_affine_transform(center, scale, rot, output_size, shift=np.array([0, 0]), inv=0)`
   - `get_affine_transform_upper_left(center, scale, rot, output_size, shift=np.array([0, 0]), inv=0)`
   - `affine_transform(pt, t)`
2. 保持所有数值计算逻辑完全不变
3. 仅调整import路径以适配train-anything环境

**受影响的现有模块**: 无，纯增量添加

**复用已有代码**: 直接复制LORE-TSR原始实现，严禁重构

**如何验证**:
```bash
# 验证仿射变换函数可正确导入和执行
cd train-anything
python -c "
from modules.utils.lore_tsr.lore_image_utils import get_affine_transform, affine_transform
import numpy as np
center = np.array([100, 100])
scale = 200
rot = 0
output_size = [256, 256]
trans = get_affine_transform(center, scale, rot, output_size)
print('仿射变换矩阵:', trans.shape)
pt = np.array([50, 50])
transformed_pt = affine_transform(pt, trans)
print('点变换结果:', transformed_pt)
print('仿射变换函数验证成功')
"
```

### 步骤5.1.3: 实现高斯相关函数

**当前迭代**: 迭代5.1 - 基础工具函数迁移
**步骤目标**: 完整迁移LORE-TSR的高斯热力图相关函数，确保热力图生成与原项目一致

**影响文件**:
- 扩展: `modules/utils/lore_tsr/lore_image_utils.py` (高斯函数部分)

**具体操作**:
1. 从LORE-TSR/src/lib/utils/image.py逐行复制以下函数:
   - `gaussian_radius(det_size, min_overlap=0.7)`
   - `draw_umich_gaussian(heatmap, center, radius, k=1)`
   - `color_aug(data_rng, image, eig_val, eig_vec)` (颜色增强函数)
2. 保持所有高斯分布计算逻辑完全不变
3. 确保热力图生成算法与原项目数值完全一致

**受影响的现有模块**: 无，纯增量添加

**复用已有代码**: 直接复制LORE-TSR原始实现，严禁重构

**如何验证**:
```bash
# 验证高斯函数可正确导入和执行
cd train-anything
python -c "
from modules.utils.lore_tsr.lore_image_utils import gaussian_radius, draw_umich_gaussian
import numpy as np
det_size = (64, 64)
radius = gaussian_radius(det_size)
print('高斯半径:', radius)
heatmap = np.zeros((128, 128), dtype=np.float32)
center = np.array([64, 64])
draw_umich_gaussian(heatmap, center, radius)
print('热力图最大值:', np.max(heatmap))
print('高斯函数验证成功')
"
```

### 步骤5.1.4: 完整验证和集成测试

**当前迭代**: 迭代5.1 - 基础工具函数迁移
**步骤目标**: 创建完整的验证测试脚本，确保所有工具函数与原LORE-TSR项目完全一致

**影响文件**:
- 创建: `test_lore_tsr_step5_1.py` (验证测试脚本)
- 创建: `test_reports/step_5_1_verification_report.md` (验证报告)

**具体操作**:
1. 创建全面的单元测试，覆盖所有迁移的函数
2. 实现数值精度对比测试，确保与原项目结果一致
3. 创建性能基准测试，验证函数执行效率
4. 生成详细的验证报告

**受影响的现有模块**: 无，纯增量添加

**复用已有代码**: 参考已完成迭代的验证测试模式

**如何验证**:
```bash
# 运行完整验证测试
cd train-anything
python test_lore_tsr_step5_1.py

# 查看验证报告
cat test_reports/step_5_1_verification_report.md

# 验证与train-anything框架的兼容性
python -c "
from modules.utils.lore_tsr.lore_image_utils import *
print('所有LORE-TSR工具函数导入成功')
print('迭代5.1验证完成')
"
```

## 🎯 验收标准

### 功能验收
1. **函数完整性**: 所有LORE-TSR/src/lib/utils/image.py中的核心函数都已正确迁移
2. **数值精度**: 仿射变换矩阵计算与原项目数值完全一致（误差<1e-6）
3. **高斯分布**: 热力图生成结果与原项目完全一致
4. **模块导入**: 所有函数可正确导入，无依赖错误

### 兼容性验收
1. **框架兼容**: 与train-anything现有模块完全兼容，无冲突
2. **依赖兼容**: 所有numpy、cv2等依赖在train-anything环境中正常工作
3. **接口预留**: 为后续迭代5.2-5.4预留合理的函数接口

### 质量验收
1. **代码质量**: 代码结构清晰，注释完整，符合Python规范
2. **测试覆盖**: 单元测试覆盖率达到100%
3. **文档完整**: 所有函数都有详细的docstring说明

## 🚨 风险控制

### 技术风险
1. **数值精度风险**: 通过严格的单元测试和对比验证控制
2. **依赖兼容风险**: 提前验证所有外部依赖的可用性
3. **函数接口风险**: 保持与原LORE-TSR完全一致的函数签名

### 集成风险
1. **模块冲突风险**: 使用独立的命名空间避免与现有模块冲突
2. **导入路径风险**: 仔细调整import路径，确保在train-anything环境中正确工作

### 项目风险
1. **进度风险**: 严格按照小步迭代执行，每步都有明确验证标准
2. **质量风险**: 通过自动化测试和详细验证报告确保质量

## 📝 后续迭代接口预留

### 为迭代5.2预留的接口
```python
# 数据集类将使用的工具函数接口
def get_lore_tsr_transform_functions():
    """返回LORE-TSR数据变换所需的所有工具函数"""
    return {
        'get_affine_transform': get_affine_transform,
        'affine_transform': affine_transform,
        'draw_umich_gaussian': draw_umich_gaussian,
        'gaussian_radius': gaussian_radius
    }
```

### 为迭代5.3预留的接口
```python
# 数据处理pipeline将使用的工具函数接口
def get_lore_tsr_processing_utils():
    """返回LORE-TSR数据处理pipeline所需的工具函数"""
    return {
        'color_aug': color_aug,
        'affine_transform_batch': affine_transform_batch  # 批处理版本
    }
```

---

**文档版本**: v1.0
**创建日期**: 2025-07-20
**适用迭代**: 迭代5.1 - 基础工具函数迁移
**依赖迭代**: 迭代1-4（已完成）
**预计工期**: 4个渐进式小步，每步0.5-1天，总计2-3个工作日
**核心交付**: 1个工具模块 + 完整验证测试
**成功关键**: 数值精度一致性 + 完整功能覆盖 + 严格验证测试
