#!/usr/bin/env python3
"""
LORE-TSR 迁移项目 - 步骤5.2验证测试脚本

迭代5步骤5.2：数据集基础框架验证
测试LoreTsrDataset的TableDataset继承和WTW到LORE格式转换

验证内容：
1. LoreTsrDataset类导入和初始化测试
2. TableDataset继承验证
3. WTW到LORE格式转换测试
4. 基础数据加载功能测试
5. 目标准备和错误处理测试
"""

import sys
import os
import torch
import numpy as np
import traceback
from pathlib import Path
from omegaconf import OmegaConf

# 添加train-anything到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def create_test_config():
    """创建测试配置"""
    config = OmegaConf.create({
        'data': {
            'dataset': {
                'data_root': r'D:\workspace\datasets\cf_train_clean\wired_tables_reorganized\TabRecSet_TableLabelMe_fix\chinese',
                'debug': True,
                'max_samples': 5  # 限制样本数量以加快测试
            },
            'processing': {
                'image_size': [512, 512],
                'down_ratio': 4,
                'max_objs': 500,
                'gaussian_iou': 0.7
            }
        },
        'model': {
            'heads': {
                'hm': 1  # 单类别
            }
        }
    })
    return config

def test_dataset_import():
    """测试数据集类导入"""
    print("=" * 60)
    print("测试1: LoreTsrDataset类导入测试")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        print("✅ LoreTsrDataset类导入成功")
        
        # 测试从模块导入
        from my_datasets.table_structure_recognition import LoreTsrDataset as LoreTsrDataset2
        print("✅ 从模块导入LoreTsrDataset成功")
        
        # 验证是否为同一个类
        assert LoreTsrDataset is LoreTsrDataset2, "导入的类不一致"
        print("✅ 类导入一致性验证通过")
        
        return True
    except ImportError as e:
        print(f"❌ 类导入失败: {e}")
        return False

def test_dataset_inheritance():
    """测试TableDataset继承"""
    print("\n" + "=" * 60)
    print("测试2: TableDataset继承验证")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        from my_datasets.table_structure_recognition.table_dataset import TableDataset
        
        # 验证继承关系
        assert issubclass(LoreTsrDataset, TableDataset), "LoreTsrDataset未正确继承TableDataset"
        print("✅ LoreTsrDataset正确继承TableDataset")
        
        # 验证方法解析顺序
        mro = LoreTsrDataset.__mro__
        print(f"✅ 方法解析顺序: {[cls.__name__ for cls in mro]}")
        
        # 验证关键方法存在
        required_methods = ['__init__', '__getitem__', '__len__']
        for method in required_methods:
            assert hasattr(LoreTsrDataset, method), f"缺少方法: {method}"
        print(f"✅ 关键方法验证通过: {required_methods}")
        
        return True
    except Exception as e:
        print(f"❌ 继承验证失败: {e}")
        traceback.print_exc()
        return False

def test_wtw_to_lore_conversion():
    """测试WTW到LORE格式转换"""
    print("\n" + "=" * 60)
    print("测试3: WTW到LORE格式转换测试")
    print("=" * 60)

    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset

        config = create_test_config()
        dataset = LoreTsrDataset(config, mode='train')
        print(f"✅ 数据集初始化成功，样本数量: {len(dataset)}")
        
        # 创建测试WTW格式数据
        wtw_annotation = {
            'annotations': [
                {
                    'id': 1,
                    'bbox': {
                        'p1': [10.0, 20.0],
                        'p2': [100.0, 20.0],
                        'p3': [100.0, 80.0],
                        'p4': [10.0, 80.0]
                    },
                    'lloc': {
                        'start_row': 0,
                        'end_row': 2,
                        'start_col': 1,
                        'end_col': 3
                    }
                }
            ],
            'image': {'width': 512, 'height': 512},
            'info': {'dataset': 'test'}
        }
        
        # 测试转换
        lore_annotation = dataset._convert_wtw_to_lore_format(wtw_annotation)
        print(f"✅ WTW到LORE转换完成")
        
        # 验证转换结果
        assert 'annotations' in lore_annotation, "缺少annotations字段"
        assert len(lore_annotation['annotations']) == 1, f"标注数量错误: {len(lore_annotation['annotations'])}"
        
        lore_ann = lore_annotation['annotations'][0]
        
        # 验证segmentation
        assert 'segmentation' in lore_ann, "缺少segmentation字段"
        segmentation = lore_ann['segmentation'][0]
        expected_seg = [10.0, 20.0, 100.0, 20.0, 100.0, 80.0, 10.0, 80.0]
        assert segmentation == expected_seg, f"segmentation转换错误: {segmentation} != {expected_seg}"
        print(f"✅ segmentation转换正确: {segmentation}")
        
        # 验证logic_axis
        assert 'logic_axis' in lore_ann, "缺少logic_axis字段"
        logic_axis = lore_ann['logic_axis']
        expected_logic = [0, 2, 1, 3]
        assert logic_axis == expected_logic, f"logic_axis转换错误: {logic_axis} != {expected_logic}"
        print(f"✅ logic_axis转换正确: {logic_axis}")
        
        # 验证category_id
        assert lore_ann['category_id'] == 1, f"category_id错误: {lore_ann['category_id']}"
        print(f"✅ category_id设置正确: {lore_ann['category_id']}")
        
        # 验证面积计算
        assert 'area' in lore_ann, "缺少area字段"
        expected_area = 90.0 * 60.0  # (100-10) * (80-20)
        assert abs(lore_ann['area'] - expected_area) < 1e-6, f"面积计算错误: {lore_ann['area']} != {expected_area}"
        print(f"✅ 面积计算正确: {lore_ann['area']}")
        
        return True
        
    except Exception as e:
        print(f"❌ WTW到LORE转换测试失败: {e}")
        traceback.print_exc()
        return False

def test_dataset_initialization():
    """测试数据集初始化"""
    print("\n" + "=" * 60)
    print("测试4: 数据集初始化测试")
    print("=" * 60)

    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset

        config = create_test_config()

        # 测试训练模式初始化
        train_dataset = LoreTsrDataset(config, mode='train')
        print(f"✅ 训练模式初始化成功，样本数量: {len(train_dataset)}")
        
        # 验证配置参数
        assert train_dataset.input_h == 512, f"input_h错误: {train_dataset.input_h}"
        assert train_dataset.input_w == 512, f"input_w错误: {train_dataset.input_w}"
        assert train_dataset.down_ratio == 4, f"down_ratio错误: {train_dataset.down_ratio}"
        assert train_dataset.output_h == 128, f"output_h错误: {train_dataset.output_h}"
        assert train_dataset.output_w == 128, f"output_w错误: {train_dataset.output_w}"
        assert train_dataset.num_classes == 1, f"num_classes错误: {train_dataset.num_classes}"
        print(f"✅ 配置参数验证通过")
        
        # 测试验证模式初始化
        val_dataset = LoreTsrDataset(config, mode='val')
        print(f"✅ 验证模式初始化成功，样本数量: {len(val_dataset)}")
        
        # 验证模式设置
        assert train_dataset.mode == 'train', f"训练模式设置错误: {train_dataset.mode}"
        assert val_dataset.mode == 'val', f"验证模式设置错误: {val_dataset.mode}"
        print(f"✅ 模式设置验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据集初始化测试失败: {e}")
        traceback.print_exc()
        return False

def test_target_preparation():
    """测试目标准备功能"""
    print("\n" + "=" * 60)
    print("测试5: 目标准备功能测试")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset

        config = create_test_config()
        dataset = LoreTsrDataset(config, mode='train')
        print(f"✅ 数据集初始化成功，样本数量: {len(dataset)}")
        
        # 测试默认目标创建
        default_targets = dataset._create_default_targets()
        print(f"✅ 默认目标创建成功")
        
        # 验证目标结构
        required_keys = ['hm', 'wh', 'reg', 'reg_mask', 'ind', 'num_objs']
        for key in required_keys:
            assert key in default_targets, f"缺少目标键: {key}"
        print(f"✅ 目标结构验证通过: {list(default_targets.keys())}")
        
        # 验证目标形状
        assert default_targets['hm'].shape == (1, 128, 128), f"hm形状错误: {default_targets['hm'].shape}"
        assert default_targets['wh'].shape == (500, 8), f"wh形状错误: {default_targets['wh'].shape}"
        assert default_targets['reg'].shape == (500, 2), f"reg形状错误: {default_targets['reg'].shape}"
        print(f"✅ 目标形状验证通过")
        
        # 验证数据类型
        assert default_targets['hm'].dtype == torch.float32, f"hm数据类型错误: {default_targets['hm'].dtype}"
        assert default_targets['ind'].dtype == torch.long, f"ind数据类型错误: {default_targets['ind'].dtype}"
        print(f"✅ 数据类型验证通过")
        
        return True

    except Exception as e:
        print(f"❌ 目标准备测试失败: {e}")
        traceback.print_exc()
        return False

def test_real_data_loading():
    """测试真实数据加载"""
    print("\n" + "=" * 60)
    print("测试6: 真实数据加载测试")
    print("=" * 60)

    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset

        config = create_test_config()
        dataset = LoreTsrDataset(config, mode='train')
        print(f"✅ 数据集加载成功，总样本数: {len(dataset)}")

        if len(dataset) > 0:
            # 测试获取第一个样本
            sample = dataset[0]
            print(f"✅ 成功获取样本0")

            # 验证样本结构
            required_keys = ['image', 'targets', 'annotation']
            for key in required_keys:
                assert key in sample, f"样本缺少键: {key}"
            print(f"✅ 样本结构验证通过: {list(sample.keys())}")

            # 验证图像
            image = sample['image']
            assert isinstance(image, torch.Tensor), f"图像类型错误: {type(image)}"
            assert len(image.shape) == 3, f"图像维度错误: {image.shape}"
            print(f"✅ 图像验证通过: shape={image.shape}, dtype={image.dtype}")

            # 验证目标
            targets = sample['targets']
            assert isinstance(targets, dict), f"目标类型错误: {type(targets)}"
            target_keys = ['hm', 'wh', 'reg', 'reg_mask', 'ind', 'num_objs']
            for key in target_keys:
                assert key in targets, f"目标缺少键: {key}"
            print(f"✅ 目标验证通过: {list(targets.keys())}")

            # 验证标注
            annotation = sample['annotation']
            assert isinstance(annotation, dict), f"标注类型错误: {type(annotation)}"
            print(f"✅ 标注验证通过")

            # 测试WTW到LORE转换
            if 'annotations' in annotation and len(annotation['annotations']) > 0:
                lore_annotation = dataset._convert_wtw_to_lore_format(annotation)
                print(f"✅ WTW到LORE转换成功，转换了{len(lore_annotation['annotations'])}个标注")

                # 验证转换结果
                if len(lore_annotation['annotations']) > 0:
                    lore_ann = lore_annotation['annotations'][0]
                    assert 'segmentation' in lore_ann, "缺少segmentation字段"
                    assert 'logic_axis' in lore_ann, "缺少logic_axis字段"
                    assert 'category_id' in lore_ann, "缺少category_id字段"
                    print(f"✅ LORE格式验证通过")
            else:
                print("⚠️  样本无标注数据，跳过转换测试")
        else:
            print("⚠️  数据集为空，跳过样本测试")

        return True

    except Exception as e:
        print(f"❌ 真实数据加载测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("LORE-TSR 迁移项目 - 步骤5.2验证测试")
    print("测试目标: 验证数据集基础框架的正确性")
    print("迁移策略: 重构适配框架入口")
    
    # 执行所有测试
    tests = [
        test_dataset_import,
        test_dataset_inheritance,
        test_wtw_to_lore_conversion,
        test_dataset_initialization,
        test_target_preparation,
        test_real_data_loading
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            results.append(False)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！步骤5.2验证成功")
        print("✅ LORE-TSR数据集基础框架实现完成")
        return True
    else:
        print("❌ 部分测试失败，需要检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
