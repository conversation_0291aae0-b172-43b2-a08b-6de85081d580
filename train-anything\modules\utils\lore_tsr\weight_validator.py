#!/usr/bin/env python3
"""
LORE-TSR 权重验证器

迭代8步骤8.2：权重加载器和验证器
负责验证权重转换的正确性和模型输出一致性
"""

import os
import torch
import numpy as np
import logging
from typing import Dict, Optional, List, Union, Tuple, Any
from dataclasses import dataclass, field
from pathlib import Path

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """验证结果数据类"""
    success: bool = False
    missing_keys: Dict[str, Any] = field(default_factory=dict)
    unexpected_keys: Dict[str, Any] = field(default_factory=dict)
    value_differences: Dict[str, Any] = field(default_factory=dict)
    error_message: str = ""
    statistics: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.statistics:
            self.statistics = {
                'total_keys': 0,
                'matched_keys': 0,
                'missing_count': 0,
                'unexpected_count': 0,
                'max_difference': 0.0,
                'mean_difference': 0.0
            }


class LoreTsrWeightValidator:
    """
    LORE-TSR权重验证器
    
    提供权重转换正确性验证、模型输出一致性检查等功能，
    确保权重迁移的可靠性
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化权重验证器
        
        Args:
            config (dict, optional): 验证器配置
        """
        self.config = config or self._get_default_config()
        self.tolerance = self.config.get('tolerance', 1e-6)
        self.save_report = self.config.get('save_report', True)
        self.validation_mode = self.config.get('mode', 'basic')
        
        logger.info(f"权重验证器初始化完成，容差: {self.tolerance}, 模式: {self.validation_mode}")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'tolerance': 1e-6,
            'save_report': True,
            'mode': 'basic',  # basic, full
            'check_shapes': True,
            'check_values': True,
            'check_statistics': True
        }
    
    def validate_converted_weights(self, original_weights: Dict, 
                                 converted_weights: Dict) -> ValidationResult:
        """
        验证转换后权重的完整性和正确性
        
        Args:
            original_weights (dict): 原始LORE-TSR权重
            converted_weights (dict): 转换后的权重
            
        Returns:
            ValidationResult: 验证结果对象
        """
        try:
            logger.info("开始验证转换后权重")
            
            result = ValidationResult()
            
            # 步骤1：检查权重完整性
            completeness_result = self.check_weight_completeness(
                converted_weights, list(original_weights.keys())
            )
            
            # 步骤2：比较权重数值
            if self.config.get('check_values', True):
                value_result = self._compare_weight_collections(
                    original_weights, converted_weights
                )
                result.value_differences = value_result
            
            # 步骤3：检查权重形状
            if self.config.get('check_shapes', True):
                shape_result = self._check_weight_shapes(
                    original_weights, converted_weights
                )
            
            # 步骤4：计算统计信息
            if self.config.get('check_statistics', True):
                result.statistics = self._calculate_validation_statistics(
                    original_weights, converted_weights
                )
            
            # 综合判断验证结果
            result.success = (
                len(result.missing_keys) == 0 and
                len(result.unexpected_keys) == 0 and
                result.statistics.get('max_difference', float('inf')) < self.tolerance
            )
            
            if result.success:
                logger.info("权重验证通过")
            else:
                logger.warning("权重验证未完全通过")
            
            return result
            
        except Exception as e:
            logger.error(f"验证转换后权重失败: {e}")
            result = ValidationResult()
            result.error_message = str(e)
            return result
    
    def validate_model_output(self, lore_model: torch.nn.Module, 
                            train_anything_model: torch.nn.Module, 
                            test_input: torch.Tensor) -> ValidationResult:
        """
        验证使用转换权重的模型输出与原模型的一致性
        
        Args:
            lore_model (nn.Module): 原LORE-TSR模型
            train_anything_model (nn.Module): 新框架模型
            test_input (torch.Tensor): 测试输入
            
        Returns:
            ValidationResult: 输出一致性验证结果
        """
        try:
            logger.info("开始验证模型输出一致性")
            
            result = ValidationResult()
            
            # 设置模型为评估模式
            lore_model.eval()
            train_anything_model.eval()
            
            with torch.no_grad():
                # 获取原模型输出
                lore_output = lore_model(test_input)
                
                # 获取新模型输出
                new_output = train_anything_model(test_input)
                
                # 比较输出
                output_diff = self._compare_model_outputs(lore_output, new_output)
                result.value_differences = output_diff
                
                # 计算统计信息
                result.statistics = self._calculate_output_statistics(lore_output, new_output)
                
                # 判断一致性
                max_diff = result.statistics.get('max_difference', float('inf'))
                result.success = max_diff < self.tolerance
                
                if result.success:
                    logger.info(f"模型输出一致性验证通过，最大差异: {max_diff}")
                else:
                    logger.warning(f"模型输出一致性验证失败，最大差异: {max_diff}")
            
            return result
            
        except Exception as e:
            logger.error(f"验证模型输出一致性失败: {e}")
            result = ValidationResult()
            result.error_message = str(e)
            return result
    
    def check_weight_completeness(self, state_dict: Dict, 
                                required_keys: List[str]) -> ValidationResult:
        """
        检查权重完整性
        
        Args:
            state_dict (dict): 权重状态字典
            required_keys (list): 必需的权重键名列表
            
        Returns:
            ValidationResult: 完整性检查结果
        """
        try:
            logger.info("检查权重完整性")
            
            result = ValidationResult()
            
            # 获取实际键名
            actual_keys = set(state_dict.keys())
            required_keys_set = set(required_keys)
            
            # 查找缺失的键
            missing_keys = required_keys_set - actual_keys
            result.missing_keys = {key: "缺失" for key in missing_keys}
            
            # 查找意外的键
            unexpected_keys = actual_keys - required_keys_set
            result.unexpected_keys = {key: "意外" for key in unexpected_keys}
            
            # 计算统计信息
            result.statistics = {
                'total_required': len(required_keys),
                'total_actual': len(actual_keys),
                'missing_count': len(missing_keys),
                'unexpected_count': len(unexpected_keys),
                'match_rate': len(actual_keys & required_keys_set) / len(required_keys_set) if required_keys_set else 0
            }
            
            # 判断完整性
            result.success = len(missing_keys) == 0
            
            logger.info(f"权重完整性检查完成，匹配率: {result.statistics['match_rate']:.2%}")
            
            return result
            
        except Exception as e:
            logger.error(f"检查权重完整性失败: {e}")
            result = ValidationResult()
            result.error_message = str(e)
            return result
    
    def compare_weight_values(self, weight1: torch.Tensor, weight2: torch.Tensor, 
                            tolerance: float = None) -> Dict[str, float]:
        """
        比较权重数值差异
        
        Args:
            weight1 (torch.Tensor): 第一个权重张量
            weight2 (torch.Tensor): 第二个权重张量
            tolerance (float, optional): 容差
            
        Returns:
            dict: 比较结果统计
        """
        tolerance = tolerance or self.tolerance
        
        try:
            # 检查形状
            if weight1.shape != weight2.shape:
                return {
                    'shape_match': False,
                    'max_difference': float('inf'),
                    'mean_difference': float('inf'),
                    'within_tolerance': False
                }
            
            # 计算差异
            diff = torch.abs(weight1 - weight2)
            max_diff = torch.max(diff).item()
            mean_diff = torch.mean(diff).item()
            
            # 检查容差
            within_tolerance = max_diff < tolerance
            
            return {
                'shape_match': True,
                'max_difference': max_diff,
                'mean_difference': mean_diff,
                'within_tolerance': within_tolerance,
                'tolerance_used': tolerance
            }
            
        except Exception as e:
            logger.error(f"比较权重数值失败: {e}")
            return {
                'error': str(e),
                'shape_match': False,
                'max_difference': float('inf'),
                'mean_difference': float('inf'),
                'within_tolerance': False
            }
    
    def generate_validation_report(self, results: ValidationResult) -> str:
        """
        生成验证报告
        
        Args:
            results (ValidationResult): 验证结果
            
        Returns:
            str: 验证报告文本
        """
        try:
            report_lines = []
            report_lines.append("LORE-TSR 权重验证报告")
            report_lines.append("=" * 50)
            report_lines.append("")
            
            # 验证结果概述
            status = "通过" if results.success else "失败"
            report_lines.append(f"验证状态: {status}")
            report_lines.append("")
            
            # 统计信息
            if results.statistics:
                report_lines.append("统计信息:")
                report_lines.append("-" * 20)
                for key, value in results.statistics.items():
                    if isinstance(value, float):
                        report_lines.append(f"  {key}: {value:.6e}")
                    else:
                        report_lines.append(f"  {key}: {value}")
                report_lines.append("")
            
            # 缺失键名
            if results.missing_keys:
                report_lines.append(f"缺失键名 ({len(results.missing_keys)} 个):")
                report_lines.append("-" * 20)
                for key in list(results.missing_keys.keys())[:10]:  # 只显示前10个
                    report_lines.append(f"  {key}")
                if len(results.missing_keys) > 10:
                    report_lines.append(f"  ... 还有 {len(results.missing_keys) - 10} 个")
                report_lines.append("")
            
            # 意外键名
            if results.unexpected_keys:
                report_lines.append(f"意外键名 ({len(results.unexpected_keys)} 个):")
                report_lines.append("-" * 20)
                for key in list(results.unexpected_keys.keys())[:10]:  # 只显示前10个
                    report_lines.append(f"  {key}")
                if len(results.unexpected_keys) > 10:
                    report_lines.append(f"  ... 还有 {len(results.unexpected_keys) - 10} 个")
                report_lines.append("")
            
            # 错误信息
            if results.error_message:
                report_lines.append("错误信息:")
                report_lines.append("-" * 20)
                report_lines.append(f"  {results.error_message}")
                report_lines.append("")
            
            return "\n".join(report_lines)
            
        except Exception as e:
            logger.error(f"生成验证报告失败: {e}")
            return f"生成验证报告失败: {str(e)}"
    
    def save_validation_report(self, report: str, output_path: str):
        """
        保存验证报告到文件
        
        Args:
            report (str): 验证报告文本
            output_path (str): 输出文件路径
        """
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"验证报告已保存: {output_path}")
            
        except Exception as e:
            logger.error(f"保存验证报告失败: {e}")
    
    def _get_required_weight_keys(self) -> List[str]:
        """获取必需的权重键名列表"""
        # 这里可以根据LORE-TSR模型结构定义必需的权重键名
        return [
            'backbone.conv1.weight',
            'backbone.conv1.bias',
            'heads.hm.weight',
            'heads.hm.bias',
            'processor.transformer.weight'
        ]
    
    def _create_test_input(self, batch_size: int = 1, height: int = 512, width: int = 512) -> torch.Tensor:
        """创建测试输入"""
        return torch.randn(batch_size, 3, height, width)
    
    def _compare_weight_collections(self, weights1: Dict, weights2: Dict) -> Dict:
        """比较权重集合"""
        differences = {}
        
        for key in weights1.keys():
            if key in weights2:
                diff_result = self.compare_weight_values(weights1[key], weights2[key])
                differences[key] = diff_result
        
        return differences
    
    def _check_weight_shapes(self, weights1: Dict, weights2: Dict) -> Dict:
        """检查权重形状"""
        shape_results = {}
        
        for key in weights1.keys():
            if key in weights2:
                shape1 = weights1[key].shape
                shape2 = weights2[key].shape
                shape_results[key] = {
                    'shape1': shape1,
                    'shape2': shape2,
                    'match': shape1 == shape2
                }
        
        return shape_results
    
    def _calculate_validation_statistics(self, weights1: Dict, weights2: Dict) -> Dict:
        """计算验证统计信息"""
        stats = {
            'total_keys': len(weights1),
            'matched_keys': 0,
            'max_difference': 0.0,
            'mean_difference': 0.0
        }
        
        differences = []
        matched_count = 0
        
        for key in weights1.keys():
            if key in weights2:
                matched_count += 1
                diff_result = self.compare_weight_values(weights1[key], weights2[key])
                if 'max_difference' in diff_result:
                    differences.append(diff_result['max_difference'])
        
        stats['matched_keys'] = matched_count
        
        if differences:
            stats['max_difference'] = max(differences)
            stats['mean_difference'] = sum(differences) / len(differences)
        
        return stats
    
    def _compare_model_outputs(self, output1: Any, output2: Any) -> Dict:
        """比较模型输出"""
        if isinstance(output1, torch.Tensor) and isinstance(output2, torch.Tensor):
            return self.compare_weight_values(output1, output2)
        elif isinstance(output1, (list, tuple)) and isinstance(output2, (list, tuple)):
            results = {}
            for i, (o1, o2) in enumerate(zip(output1, output2)):
                results[f'output_{i}'] = self._compare_model_outputs(o1, o2)
            return results
        else:
            return {'error': 'Unsupported output type'}
    
    def _calculate_output_statistics(self, output1: Any, output2: Any) -> Dict:
        """计算输出统计信息"""
        if isinstance(output1, torch.Tensor) and isinstance(output2, torch.Tensor):
            diff = torch.abs(output1 - output2)
            return {
                'max_difference': torch.max(diff).item(),
                'mean_difference': torch.mean(diff).item(),
                'output_shape': output1.shape
            }
        else:
            return {'error': 'Cannot calculate statistics for non-tensor outputs'}
