#!/usr/bin/env python3
"""
LORE-TSR 权重处理基础工具函数

迭代8步骤8.1：权重处理基础设施
提供权重文件处理、格式检测、键名映射等基础功能
"""

import os
import re
import torch
import logging
from typing import Dict, List, Optional, Union, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


def remove_module_prefix(key: str) -> str:
    """
    移除DataParallel添加的'module.'前缀
    
    Args:
        key (str): 权重键名
        
    Returns:
        str: 处理后的键名
        
    Examples:
        >>> remove_module_prefix('module.backbone.conv1.weight')
        'backbone.conv1.weight'
        >>> remove_module_prefix('backbone.conv1.weight')
        'backbone.conv1.weight'
    """
    if key.startswith('module.'):
        return key[7:]  # 移除 'module.' 前缀
    return key


def detect_checkpoint_format(checkpoint_path: str) -> str:
    """
    自动检测权重文件格式
    
    Args:
        checkpoint_path (str): 权重文件路径
        
    Returns:
        str: 格式类型 ('lore', 'train_anything', 'unknown')
        
    Examples:
        >>> detect_checkpoint_format('/path/to/model_best.pth')
        'lore'
        >>> detect_checkpoint_format('/path/to/pytorch_model.bin')
        'train_anything'
    """
    if not os.path.exists(checkpoint_path):
        logger.warning(f"权重文件不存在: {checkpoint_path}")
        return 'unknown'
    
    try:
        # 尝试加载权重文件
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # 检查是否为LORE-TSR格式
        if isinstance(checkpoint, dict):
            # LORE-TSR格式通常包含这些键
            lore_indicators = ['state_dict', 'epoch', 'best_result']
            if any(key in checkpoint for key in lore_indicators):
                return 'lore'
            
            # 检查权重键名模式
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint
                
            # 检查键名模式
            keys = list(state_dict.keys())
            if keys:
                # LORE-TSR模式：通常有module前缀
                if any(key.startswith('module.') for key in keys):
                    return 'lore'
                # train-anything模式：通常有backbone/processor前缀
                if any(key.startswith(('backbone.', 'processor.')) for key in keys):
                    return 'train_anything'
        
        return 'unknown'
        
    except Exception as e:
        logger.error(f"检测权重格式时出错: {e}")
        return 'unknown'


def create_weight_mapping_rules() -> Dict[str, Dict[str, str]]:
    """
    创建默认的权重键名映射规则
    
    Returns:
        dict: 映射规则字典
        
    格式:
        {
            'pattern_type': {
                'lore_pattern': 'LORE-TSR键名模式',
                'train_anything_pattern': 'train-anything键名模式',
                'component_type': '组件类型(model/processor)',
                'description': '规则描述'
            }
        }
    """
    mapping_rules = {
        # 模型主干网络映射
        'backbone_basic': {
            'lore_pattern': r'^module\.backbone\.(.+)$',
            'train_anything_pattern': r'backbone.\1',
            'component_type': 'model',
            'description': '主干网络基础映射'
        },
        
        # 检测头映射
        'detection_heads': {
            'lore_pattern': r'^module\.heads\.(.+)$',
            'train_anything_pattern': r'backbone.heads.\1',
            'component_type': 'model',
            'description': '检测头映射到backbone下'
        },
        
        # Transformer组件映射
        'transformer': {
            'lore_pattern': r'^transformer\.(.+)$',
            'train_anything_pattern': r'processor.transformer.\1',
            'component_type': 'processor',
            'description': 'Transformer组件映射到processor下'
        },
        
        # 位置嵌入映射
        'position_embedding': {
            'lore_pattern': r'^pos_embed\.(.+)$',
            'train_anything_pattern': r'processor.pos_embed.\1',
            'component_type': 'processor',
            'description': '位置嵌入映射到processor下'
        },
        
        # 分类器映射
        'classifier': {
            'lore_pattern': r'^classifier\.(.+)$',
            'train_anything_pattern': r'processor.classifier.\1',
            'component_type': 'processor',
            'description': '分类器映射到processor下'
        },
        
        # 直接映射（无前缀）
        'direct_mapping': {
            'lore_pattern': r'^(?!module\.)(.+)$',
            'train_anything_pattern': r'backbone.\1',
            'component_type': 'model',
            'description': '无前缀权重直接映射到backbone下'
        }
    }
    
    return mapping_rules


def validate_weight_file(file_path: str) -> bool:
    """
    验证权重文件是否有效
    
    Args:
        file_path (str): 权重文件路径
        
    Returns:
        bool: 文件是否有效
    """
    if not os.path.exists(file_path):
        logger.error(f"权重文件不存在: {file_path}")
        return False
    
    try:
        # 尝试加载权重文件
        checkpoint = torch.load(file_path, map_location='cpu')
        
        # 检查是否为字典格式
        if not isinstance(checkpoint, dict):
            logger.error(f"权重文件格式无效: {file_path}")
            return False
        
        # 检查是否包含权重数据
        if 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        else:
            state_dict = checkpoint
            
        if not isinstance(state_dict, dict) or len(state_dict) == 0:
            logger.error(f"权重文件不包含有效的状态字典: {file_path}")
            return False
        
        logger.info(f"权重文件验证通过: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"验证权重文件时出错: {e}")
        return False


def get_weight_file_info(file_path: str) -> Dict[str, Union[str, int, List[str]]]:
    """
    获取权重文件的基本信息
    
    Args:
        file_path (str): 权重文件路径
        
    Returns:
        dict: 权重文件信息
    """
    info = {
        'file_path': file_path,
        'exists': False,
        'format': 'unknown',
        'size_mb': 0,
        'num_parameters': 0,
        'key_count': 0,
        'sample_keys': [],
        'has_optimizer_state': False,
        'epoch': None,
        'error': None
    }
    
    try:
        if not os.path.exists(file_path):
            info['error'] = 'File not found'
            return info
        
        info['exists'] = True
        info['size_mb'] = round(os.path.getsize(file_path) / (1024 * 1024), 2)
        
        # 加载权重文件
        checkpoint = torch.load(file_path, map_location='cpu')
        
        if isinstance(checkpoint, dict):
            # 检测格式
            info['format'] = detect_checkpoint_format(file_path)
            
            # 获取状态字典
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
                info['has_optimizer_state'] = 'optimizer' in checkpoint
                info['epoch'] = checkpoint.get('epoch', None)
            else:
                state_dict = checkpoint
            
            # 统计参数信息
            info['key_count'] = len(state_dict)
            info['sample_keys'] = list(state_dict.keys())[:10]  # 前10个键名作为样本
            
            # 计算参数总数
            total_params = 0
            for key, tensor in state_dict.items():
                if isinstance(tensor, torch.Tensor):
                    total_params += tensor.numel()
            info['num_parameters'] = total_params
        
    except Exception as e:
        info['error'] = str(e)
        logger.error(f"获取权重文件信息时出错: {e}")
    
    return info


def apply_weight_key_mapping(key: str, mapping_rules: Dict[str, Dict[str, str]], 
                           component_type: str = 'model') -> str:
    """
    应用权重键名映射规则
    
    Args:
        key (str): 原始键名
        mapping_rules (dict): 映射规则字典
        component_type (str): 组件类型 ('model' 或 'processor')
        
    Returns:
        str: 映射后的键名
    """
    # 首先移除module前缀
    clean_key = remove_module_prefix(key)
    
    # 应用映射规则
    for rule_name, rule in mapping_rules.items():
        if rule['component_type'] != component_type:
            continue
            
        pattern = rule['lore_pattern']
        replacement = rule['train_anything_pattern']
        
        match = re.match(pattern, clean_key)
        if match:
            # 应用替换
            mapped_key = re.sub(pattern, replacement, clean_key)
            logger.debug(f"键名映射: {key} -> {mapped_key} (规则: {rule_name})")
            return mapped_key
    
    # 如果没有匹配的规则，返回清理后的键名
    logger.warning(f"未找到匹配的映射规则: {key}")
    return clean_key


def get_checkpoint_save_path(output_dir: str, prefix: str = "converted") -> str:
    """
    生成检查点保存路径
    
    Args:
        output_dir (str): 输出目录
        prefix (str): 文件名前缀
        
    Returns:
        str: 完整的保存路径
    """
    os.makedirs(output_dir, exist_ok=True)
    filename = f"{prefix}_pytorch_model.bin"
    return os.path.join(output_dir, filename)
