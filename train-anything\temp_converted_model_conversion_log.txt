LORE-TSR 权重转换日志
==================================================

模型权重路径: D:/workspace/projects/TSRTransplantation/LORE-TSR/src/ckpt_wireless/model_best.pth
处理器权重路径: D:/workspace/projects/TSRTransplantation/LORE-TSR/src/ckpt_wireless/processor_best.pth
输出路径: D:/workspace/projects/TSRTransplantation/train-anything/temp_converted_model.bin
转换后参数数量: 377

转换后权重键名列表:
------------------------------
backbone.adaption0.weight
backbone.adaption1.weight
backbone.adaption2.weight
backbone.adaption3.weight
backbone.adaptionU1.weight
backbone.ax.0.bias
backbone.ax.0.weight
backbone.ax.2.bias
backbone.ax.2.weight
backbone.ax.4.bias
backbone.ax.4.weight
backbone.ax.6.bias
backbone.ax.6.weight
backbone.ax.8.bias
backbone.ax.8.weight
backbone.bn1.bias
backbone.bn1.num_batches_tracked
backbone.bn1.running_mean
backbone.bn1.running_var
backbone.bn1.weight
backbone.conv1.weight
backbone.cr.0.bias
backbone.cr.0.weight
backbone.cr.2.bias
backbone.cr.2.weight
backbone.cr.4.bias
backbone.cr.4.weight
backbone.cr.6.bias
backbone.cr.6.weight
backbone.cr.8.bias
backbone.cr.8.weight
backbone.deconv_layers1.0.weight
backbone.deconv_layers1.1.bias
backbone.deconv_layers1.1.num_batches_tracked
backbone.deconv_layers1.1.running_mean
backbone.deconv_layers1.1.running_var
backbone.deconv_layers1.1.weight
backbone.deconv_layers2.0.weight
backbone.deconv_layers2.1.bias
backbone.deconv_layers2.1.num_batches_tracked
backbone.deconv_layers2.1.running_mean
backbone.deconv_layers2.1.running_var
backbone.deconv_layers2.1.weight
backbone.deconv_layers3.0.weight
backbone.deconv_layers3.1.bias
backbone.deconv_layers3.1.num_batches_tracked
backbone.deconv_layers3.1.running_mean
backbone.deconv_layers3.1.running_var
backbone.deconv_layers3.1.weight
backbone.deconv_layers4.0.weight
backbone.deconv_layers4.1.bias
backbone.deconv_layers4.1.num_batches_tracked
backbone.deconv_layers4.1.running_mean
backbone.deconv_layers4.1.running_var
backbone.deconv_layers4.1.weight
backbone.hm.0.bias
backbone.hm.0.weight
backbone.hm.2.bias
backbone.hm.2.weight
backbone.hm.4.bias
backbone.hm.4.weight
backbone.hm.6.bias
backbone.hm.6.weight
backbone.hm.8.bias
backbone.hm.8.weight
backbone.layer1.0.bn1.bias
backbone.layer1.0.bn1.num_batches_tracked
backbone.layer1.0.bn1.running_mean
backbone.layer1.0.bn1.running_var
backbone.layer1.0.bn1.weight
backbone.layer1.0.bn2.bias
backbone.layer1.0.bn2.num_batches_tracked
backbone.layer1.0.bn2.running_mean
backbone.layer1.0.bn2.running_var
backbone.layer1.0.bn2.weight
backbone.layer1.0.conv1.bias
backbone.layer1.0.conv1.weight
backbone.layer1.0.conv2.bias
backbone.layer1.0.conv2.weight
backbone.layer1.0.downsample.0.weight
backbone.layer1.0.downsample.1.bias
backbone.layer1.0.downsample.1.num_batches_tracked
backbone.layer1.0.downsample.1.running_mean
backbone.layer1.0.downsample.1.running_var
backbone.layer1.0.downsample.1.weight
backbone.layer1.1.bn1.bias
backbone.layer1.1.bn1.num_batches_tracked
backbone.layer1.1.bn1.running_mean
backbone.layer1.1.bn1.running_var
backbone.layer1.1.bn1.weight
backbone.layer1.1.bn2.bias
backbone.layer1.1.bn2.num_batches_tracked
backbone.layer1.1.bn2.running_mean
backbone.layer1.1.bn2.running_var
backbone.layer1.1.bn2.weight
backbone.layer1.1.conv1.bias
backbone.layer1.1.conv1.weight
backbone.layer1.1.conv2.bias
backbone.layer1.1.conv2.weight
backbone.layer2.0.bn1.bias
backbone.layer2.0.bn1.num_batches_tracked
backbone.layer2.0.bn1.running_mean
backbone.layer2.0.bn1.running_var
backbone.layer2.0.bn1.weight
backbone.layer2.0.bn2.bias
backbone.layer2.0.bn2.num_batches_tracked
backbone.layer2.0.bn2.running_mean
backbone.layer2.0.bn2.running_var
backbone.layer2.0.bn2.weight
backbone.layer2.0.conv1.bias
backbone.layer2.0.conv1.weight
backbone.layer2.0.conv2.bias
backbone.layer2.0.conv2.weight
backbone.layer2.0.downsample.0.weight
backbone.layer2.0.downsample.1.bias
backbone.layer2.0.downsample.1.num_batches_tracked
backbone.layer2.0.downsample.1.running_mean
backbone.layer2.0.downsample.1.running_var
backbone.layer2.0.downsample.1.weight
backbone.layer2.1.bn1.bias
backbone.layer2.1.bn1.num_batches_tracked
backbone.layer2.1.bn1.running_mean
backbone.layer2.1.bn1.running_var
backbone.layer2.1.bn1.weight
backbone.layer2.1.bn2.bias
backbone.layer2.1.bn2.num_batches_tracked
backbone.layer2.1.bn2.running_mean
backbone.layer2.1.bn2.running_var
backbone.layer2.1.bn2.weight
backbone.layer2.1.conv1.bias
backbone.layer2.1.conv1.weight
backbone.layer2.1.conv2.bias
backbone.layer2.1.conv2.weight
backbone.layer3.0.bn1.bias
backbone.layer3.0.bn1.num_batches_tracked
backbone.layer3.0.bn1.running_mean
backbone.layer3.0.bn1.running_var
backbone.layer3.0.bn1.weight
backbone.layer3.0.bn2.bias
backbone.layer3.0.bn2.num_batches_tracked
backbone.layer3.0.bn2.running_mean
backbone.layer3.0.bn2.running_var
backbone.layer3.0.bn2.weight
backbone.layer3.0.conv1.bias
backbone.layer3.0.conv1.weight
backbone.layer3.0.conv2.bias
backbone.layer3.0.conv2.weight
backbone.layer3.0.downsample.0.weight
backbone.layer3.0.downsample.1.bias
backbone.layer3.0.downsample.1.num_batches_tracked
backbone.layer3.0.downsample.1.running_mean
backbone.layer3.0.downsample.1.running_var
backbone.layer3.0.downsample.1.weight
backbone.layer3.1.bn1.bias
backbone.layer3.1.bn1.num_batches_tracked
backbone.layer3.1.bn1.running_mean
backbone.layer3.1.bn1.running_var
backbone.layer3.1.bn1.weight
backbone.layer3.1.bn2.bias
backbone.layer3.1.bn2.num_batches_tracked
backbone.layer3.1.bn2.running_mean
backbone.layer3.1.bn2.running_var
backbone.layer3.1.bn2.weight
backbone.layer3.1.conv1.bias
backbone.layer3.1.conv1.weight
backbone.layer3.1.conv2.bias
backbone.layer3.1.conv2.weight
backbone.layer4.0.bn1.bias
backbone.layer4.0.bn1.num_batches_tracked
backbone.layer4.0.bn1.running_mean
backbone.layer4.0.bn1.running_var
backbone.layer4.0.bn1.weight
backbone.layer4.0.bn2.bias
backbone.layer4.0.bn2.num_batches_tracked
backbone.layer4.0.bn2.running_mean
backbone.layer4.0.bn2.running_var
backbone.layer4.0.bn2.weight
backbone.layer4.0.conv1.bias
backbone.layer4.0.conv1.weight
backbone.layer4.0.conv2.bias
backbone.layer4.0.conv2.weight
backbone.layer4.0.downsample.0.weight
backbone.layer4.0.downsample.1.bias
backbone.layer4.0.downsample.1.num_batches_tracked
backbone.layer4.0.downsample.1.running_mean
backbone.layer4.0.downsample.1.running_var
backbone.layer4.0.downsample.1.weight
backbone.layer4.1.bn1.bias
backbone.layer4.1.bn1.num_batches_tracked
backbone.layer4.1.bn1.running_mean
backbone.layer4.1.bn1.running_var
backbone.layer4.1.bn1.weight
backbone.layer4.1.bn2.bias
backbone.layer4.1.bn2.num_batches_tracked
backbone.layer4.1.bn2.running_mean
backbone.layer4.1.bn2.running_var
backbone.layer4.1.bn2.weight
backbone.layer4.1.conv1.bias
backbone.layer4.1.conv1.weight
backbone.layer4.1.conv2.bias
backbone.layer4.1.conv2.weight
backbone.reg.0.bias
backbone.reg.0.weight
backbone.reg.2.bias
backbone.reg.2.weight
backbone.st.0.bias
backbone.st.0.weight
backbone.st.2.bias
backbone.st.2.weight
backbone.st.4.bias
backbone.st.4.weight
backbone.st.6.bias
backbone.st.6.weight
backbone.st.8.bias
backbone.st.8.weight
backbone.wh.0.bias
backbone.wh.0.weight
backbone.wh.2.bias
backbone.wh.2.weight
backbone.wh.4.bias
backbone.wh.4.weight
backbone.wh.6.bias
backbone.wh.6.weight
backbone.wh.8.bias
backbone.wh.8.weight
stacker.logi_encoder.0.bias
stacker.logi_encoder.0.weight
stacker.logi_encoder.2.bias
stacker.logi_encoder.2.weight
stacker.tsfm.decoder.linear.0.bias
stacker.tsfm.decoder.linear.0.weight
stacker.tsfm.decoder.linear.2.bias
stacker.tsfm.decoder.linear.2.weight
stacker.tsfm.encoder.layers.0.attn.k_linear.bias
stacker.tsfm.encoder.layers.0.attn.k_linear.weight
stacker.tsfm.encoder.layers.0.attn.out.bias
stacker.tsfm.encoder.layers.0.attn.out.weight
stacker.tsfm.encoder.layers.0.attn.q_linear.bias
stacker.tsfm.encoder.layers.0.attn.q_linear.weight
stacker.tsfm.encoder.layers.0.attn.v_linear.bias
stacker.tsfm.encoder.layers.0.attn.v_linear.weight
stacker.tsfm.encoder.layers.0.ff.linear_1.bias
stacker.tsfm.encoder.layers.0.ff.linear_1.weight
stacker.tsfm.encoder.layers.0.ff.linear_2.bias
stacker.tsfm.encoder.layers.0.ff.linear_2.weight
stacker.tsfm.encoder.layers.0.norm_1.alpha
stacker.tsfm.encoder.layers.0.norm_1.bias
stacker.tsfm.encoder.layers.0.norm_2.alpha
stacker.tsfm.encoder.layers.0.norm_2.bias
stacker.tsfm.encoder.layers.1.attn.k_linear.bias
stacker.tsfm.encoder.layers.1.attn.k_linear.weight
stacker.tsfm.encoder.layers.1.attn.out.bias
stacker.tsfm.encoder.layers.1.attn.out.weight
stacker.tsfm.encoder.layers.1.attn.q_linear.bias
stacker.tsfm.encoder.layers.1.attn.q_linear.weight
stacker.tsfm.encoder.layers.1.attn.v_linear.bias
stacker.tsfm.encoder.layers.1.attn.v_linear.weight
stacker.tsfm.encoder.layers.1.ff.linear_1.bias
stacker.tsfm.encoder.layers.1.ff.linear_1.weight
stacker.tsfm.encoder.layers.1.ff.linear_2.bias
stacker.tsfm.encoder.layers.1.ff.linear_2.weight
stacker.tsfm.encoder.layers.1.norm_1.alpha
stacker.tsfm.encoder.layers.1.norm_1.bias
stacker.tsfm.encoder.layers.1.norm_2.alpha
stacker.tsfm.encoder.layers.1.norm_2.bias
stacker.tsfm.encoder.layers.2.attn.k_linear.bias
stacker.tsfm.encoder.layers.2.attn.k_linear.weight
stacker.tsfm.encoder.layers.2.attn.out.bias
stacker.tsfm.encoder.layers.2.attn.out.weight
stacker.tsfm.encoder.layers.2.attn.q_linear.bias
stacker.tsfm.encoder.layers.2.attn.q_linear.weight
stacker.tsfm.encoder.layers.2.attn.v_linear.bias
stacker.tsfm.encoder.layers.2.attn.v_linear.weight
stacker.tsfm.encoder.layers.2.ff.linear_1.bias
stacker.tsfm.encoder.layers.2.ff.linear_1.weight
stacker.tsfm.encoder.layers.2.ff.linear_2.bias
stacker.tsfm.encoder.layers.2.ff.linear_2.weight
stacker.tsfm.encoder.layers.2.norm_1.alpha
stacker.tsfm.encoder.layers.2.norm_1.bias
stacker.tsfm.encoder.layers.2.norm_2.alpha
stacker.tsfm.encoder.layers.2.norm_2.bias
stacker.tsfm.encoder.layers.3.attn.k_linear.bias
stacker.tsfm.encoder.layers.3.attn.k_linear.weight
stacker.tsfm.encoder.layers.3.attn.out.bias
stacker.tsfm.encoder.layers.3.attn.out.weight
stacker.tsfm.encoder.layers.3.attn.q_linear.bias
stacker.tsfm.encoder.layers.3.attn.q_linear.weight
stacker.tsfm.encoder.layers.3.attn.v_linear.bias
stacker.tsfm.encoder.layers.3.attn.v_linear.weight
stacker.tsfm.encoder.layers.3.ff.linear_1.bias
stacker.tsfm.encoder.layers.3.ff.linear_1.weight
stacker.tsfm.encoder.layers.3.ff.linear_2.bias
stacker.tsfm.encoder.layers.3.ff.linear_2.weight
stacker.tsfm.encoder.layers.3.norm_1.alpha
stacker.tsfm.encoder.layers.3.norm_1.bias
stacker.tsfm.encoder.layers.3.norm_2.alpha
stacker.tsfm.encoder.layers.3.norm_2.bias
stacker.tsfm.encoder.norm.alpha
stacker.tsfm.encoder.norm.bias
stacker.tsfm.encoder.pe.pe
stacker.tsfm.linear.bias
stacker.tsfm.linear.weight
tsfm_axis.decoder.linear.0.bias
tsfm_axis.decoder.linear.0.weight
tsfm_axis.decoder.linear.2.bias
tsfm_axis.decoder.linear.2.weight
tsfm_axis.encoder.layers.0.attn.k_linear.bias
tsfm_axis.encoder.layers.0.attn.k_linear.weight
tsfm_axis.encoder.layers.0.attn.out.bias
tsfm_axis.encoder.layers.0.attn.out.weight
tsfm_axis.encoder.layers.0.attn.q_linear.bias
tsfm_axis.encoder.layers.0.attn.q_linear.weight
tsfm_axis.encoder.layers.0.attn.v_linear.bias
tsfm_axis.encoder.layers.0.attn.v_linear.weight
tsfm_axis.encoder.layers.0.ff.linear_1.bias
tsfm_axis.encoder.layers.0.ff.linear_1.weight
tsfm_axis.encoder.layers.0.ff.linear_2.bias
tsfm_axis.encoder.layers.0.ff.linear_2.weight
tsfm_axis.encoder.layers.0.norm_1.alpha
tsfm_axis.encoder.layers.0.norm_1.bias
tsfm_axis.encoder.layers.0.norm_2.alpha
tsfm_axis.encoder.layers.0.norm_2.bias
tsfm_axis.encoder.layers.1.attn.k_linear.bias
tsfm_axis.encoder.layers.1.attn.k_linear.weight
tsfm_axis.encoder.layers.1.attn.out.bias
tsfm_axis.encoder.layers.1.attn.out.weight
tsfm_axis.encoder.layers.1.attn.q_linear.bias
tsfm_axis.encoder.layers.1.attn.q_linear.weight
tsfm_axis.encoder.layers.1.attn.v_linear.bias
tsfm_axis.encoder.layers.1.attn.v_linear.weight
tsfm_axis.encoder.layers.1.ff.linear_1.bias
tsfm_axis.encoder.layers.1.ff.linear_1.weight
tsfm_axis.encoder.layers.1.ff.linear_2.bias
tsfm_axis.encoder.layers.1.ff.linear_2.weight
tsfm_axis.encoder.layers.1.norm_1.alpha
tsfm_axis.encoder.layers.1.norm_1.bias
tsfm_axis.encoder.layers.1.norm_2.alpha
tsfm_axis.encoder.layers.1.norm_2.bias
tsfm_axis.encoder.layers.2.attn.k_linear.bias
tsfm_axis.encoder.layers.2.attn.k_linear.weight
tsfm_axis.encoder.layers.2.attn.out.bias
tsfm_axis.encoder.layers.2.attn.out.weight
tsfm_axis.encoder.layers.2.attn.q_linear.bias
tsfm_axis.encoder.layers.2.attn.q_linear.weight
tsfm_axis.encoder.layers.2.attn.v_linear.bias
tsfm_axis.encoder.layers.2.attn.v_linear.weight
tsfm_axis.encoder.layers.2.ff.linear_1.bias
tsfm_axis.encoder.layers.2.ff.linear_1.weight
tsfm_axis.encoder.layers.2.ff.linear_2.bias
tsfm_axis.encoder.layers.2.ff.linear_2.weight
tsfm_axis.encoder.layers.2.norm_1.alpha
tsfm_axis.encoder.layers.2.norm_1.bias
tsfm_axis.encoder.layers.2.norm_2.alpha
tsfm_axis.encoder.layers.2.norm_2.bias
tsfm_axis.encoder.layers.3.attn.k_linear.bias
tsfm_axis.encoder.layers.3.attn.k_linear.weight
tsfm_axis.encoder.layers.3.attn.out.bias
tsfm_axis.encoder.layers.3.attn.out.weight
tsfm_axis.encoder.layers.3.attn.q_linear.bias
tsfm_axis.encoder.layers.3.attn.q_linear.weight
tsfm_axis.encoder.layers.3.attn.v_linear.bias
tsfm_axis.encoder.layers.3.attn.v_linear.weight
tsfm_axis.encoder.layers.3.ff.linear_1.bias
tsfm_axis.encoder.layers.3.ff.linear_1.weight
tsfm_axis.encoder.layers.3.ff.linear_2.bias
tsfm_axis.encoder.layers.3.ff.linear_2.weight
tsfm_axis.encoder.layers.3.norm_1.alpha
tsfm_axis.encoder.layers.3.norm_1.bias
tsfm_axis.encoder.layers.3.norm_2.alpha
tsfm_axis.encoder.layers.3.norm_2.bias
tsfm_axis.encoder.norm.alpha
tsfm_axis.encoder.norm.bias
tsfm_axis.encoder.pe.pe
tsfm_axis.linear.bias
tsfm_axis.linear.weight
x_position_embeddings.weight
y_position_embeddings.weight
