#!/usr/bin/env python3
"""
LORE-TSR 迁移验证测试 - 步骤6.1：Transformer组件基础实现

验证内容：
1. Transformer组件导入测试
2. 组件实例化测试
3. 前向传播功能测试
4. 项目完整性验证

Time: 2025-07-20
Author: LORE-TSR Migration Team
"""

import sys
import torch
import traceback
from omegaconf import OmegaConf

def test_transformer_import():
    """测试Transformer组件导入"""
    print("=" * 60)
    print("测试1: Transformer组件导入测试")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.transformer import (
            Transformer, 
            MultiHeadAttention, 
            Encoder, 
            Decoder,
            EncoderLayer,
            FeedForward,
            Norm,
            PositionalEncoder,
            Embedder,
            DecoderLayer,
            get_clones,
            attention,
            attention_score
        )
        print("✅ 所有Transformer组件导入成功")
        return True
    except Exception as e:
        print(f"❌ Transformer组件导入失败: {e}")
        traceback.print_exc()
        return False

def test_transformer_instantiation():
    """测试Transformer组件实例化"""
    print("\n" + "=" * 60)
    print("测试2: Transformer组件实例化测试")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.transformer import Transformer
        
        # 使用配置文件中的参数
        transformer = Transformer(
            input_size=256, 
            hidden_size=256, 
            output_size=4, 
            n_layers=6, 
            heads=8, 
            dropout=0.1
        )
        
        param_count = sum(p.numel() for p in transformer.parameters())
        print(f"✅ Transformer实例化成功: {param_count:,} 个参数")
        
        # 测试各个子组件
        print(f"  - Encoder层数: {transformer.encoder.N}")
        print(f"  - 输入维度: {transformer.linear.in_features}")
        print(f"  - 隐藏维度: {transformer.linear.out_features}")
        print(f"  - 输出维度: {transformer.decoder.linear[-2].out_features}")
        
        return True
    except Exception as e:
        print(f"❌ Transformer实例化失败: {e}")
        traceback.print_exc()
        return False

def test_transformer_forward():
    """测试Transformer前向传播"""
    print("\n" + "=" * 60)
    print("测试3: Transformer前向传播测试")
    print("=" * 60)
    
    try:
        from networks.lore_tsr.transformer import Transformer
        
        transformer = Transformer(
            input_size=256, 
            hidden_size=256, 
            output_size=4, 
            n_layers=6, 
            heads=8, 
            dropout=0.1
        )
        
        # 测试推理模式（无mask）
        batch_size, seq_len, input_dim = 2, 100, 256
        x = torch.randn(batch_size, seq_len, input_dim)
        
        print(f"输入张量形状: {x.shape}")
        
        # 基础前向传播
        output = transformer(x)
        print(f"✅ 推理模式前向传播成功: 输入{x.shape} -> 输出{output.shape}")
        
        # 带注意力权重的前向传播
        output_with_att, att = transformer(x, require_att=True)
        print(f"✅ 注意力模式前向传播成功: 输出{output_with_att.shape}, 注意力{att.shape}")
        
        # 测试训练模式（带mask）
        mask = torch.ones(batch_size, seq_len)  # 简单的mask
        output_train = transformer(x, mask=mask)
        print(f"✅ 训练模式前向传播成功: 输出{output_train.shape}")
        
        # 验证输出维度正确性
        expected_shape = (batch_size, seq_len, 4)
        assert output.shape == expected_shape, f"输出形状不匹配: 期望{expected_shape}, 实际{output.shape}"
        print(f"✅ 输出维度验证通过: {output.shape}")
        
        return True
    except Exception as e:
        print(f"❌ Transformer前向传播失败: {e}")
        traceback.print_exc()
        return False

def test_project_integrity():
    """测试项目完整性"""
    print("\n" + "=" * 60)
    print("测试4: 项目完整性验证")
    print("=" * 60)
    
    try:
        # 测试所有现有组件仍然可用
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
        from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
        from networks.lore_tsr.transformer import Transformer
        from omegaconf import OmegaConf
        
        print("✅ 数据集组件导入成功")
        print("✅ 模型组件导入成功")
        print("✅ 损失函数组件导入成功")
        print("✅ Transformer组件导入成功")
        print("✅ 配置系统导入成功")
        
        # 测试配置文件加载
        config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
        print("✅ 配置文件加载成功")
        
        # 验证processor配置节存在
        if hasattr(config, 'processor'):
            print("✅ processor配置节存在")
            print(f"  - Transformer层数: {config.processor.tsfm_layers}")
            print(f"  - 隐藏层大小: {config.processor.hidden_size}")
            print(f"  - 输入大小: {config.processor.input_size}")
            print(f"  - 输出大小: {config.processor.output_size}")
        else:
            print("⚠️  processor配置节不存在，但不影响当前测试")
        
        return True
    except Exception as e:
        print(f"❌ 项目完整性验证失败: {e}")
        traceback.print_exc()
        return False

def test_component_integration():
    """测试组件集成"""
    print("\n" + "=" * 60)
    print("测试5: 组件集成测试")
    print("=" * 60)
    
    try:
        from networks.lore_tsr import (
            Transformer,
            MultiHeadAttention,
            Encoder,
            Decoder
        )
        
        print("✅ 通过networks.lore_tsr导入Transformer组件成功")
        
        # 测试子组件独立使用
        attention = MultiHeadAttention(heads=8, d_model=256, dropout=0.1)
        encoder = Encoder(input_size=256, hidden_size=256, N=6, heads=8, dropout=0.1)
        decoder = Decoder(hidden_size=256, output_size=4)
        
        print("✅ 子组件独立实例化成功")
        
        # 测试子组件前向传播
        x = torch.randn(2, 100, 256)
        att_out = attention(x, x, x)
        enc_out = encoder(x)
        dec_out = decoder(enc_out)
        
        print(f"✅ 子组件前向传播成功:")
        print(f"  - MultiHeadAttention: {x.shape} -> {att_out.shape}")
        print(f"  - Encoder: {x.shape} -> {enc_out.shape}")
        print(f"  - Decoder: {enc_out.shape} -> {dec_out.shape}")
        
        return True
    except Exception as e:
        print(f"❌ 组件集成测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始LORE-TSR步骤6.1验证测试")
    print("测试目标：Transformer组件基础实现")
    
    test_results = []
    
    # 执行所有测试
    test_results.append(("Transformer组件导入", test_transformer_import()))
    test_results.append(("Transformer组件实例化", test_transformer_instantiation()))
    test_results.append(("Transformer前向传播", test_transformer_forward()))
    test_results.append(("项目完整性验证", test_project_integrity()))
    test_results.append(("组件集成测试", test_component_integration()))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 步骤6.1验证测试全部通过！")
        print("✅ Transformer组件基础实现成功")
        print("✅ 项目保持可运行状态")
        print("✅ 为步骤6.2 Processor组件实现提供基础支持")
        return True
    else:
        print("❌ 部分测试失败，需要修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
