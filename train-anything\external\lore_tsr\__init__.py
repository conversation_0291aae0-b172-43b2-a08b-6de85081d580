#!/usr/bin/env python3
"""
LORE-TSR 外部依赖模块初始化文件

Time: 2025-07-18
Author: LORE-TSR Migration Team
Description: LORE-TSR需要的外部编译依赖，包括DCNv2、NMS、cocoapi等
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "LORE-TSR Team"

# 迭代7.1：DCNv2真实实现已集成
# DCNv2自带完善的容错机制，无需额外处理
from .DCNv2 import DCN, DCNv2

# 迭代7.2：NMS模块已集成
from .NMS import NMS_CYTHON_AVAILABLE, SHAPELY_NMS_AVAILABLE

# 迭代7.3：cocoapi模块已集成
from .cocoapi import COCO_AVAILABLE

__all__ = [
    "__version__",
    "DCN",
    "DCNv2",
    "NMS_CYTHON_AVAILABLE",
    "SHAPELY_NMS_AVAILABLE",
    "COCO_AVAILABLE",
]

# 条件导入NMS函数
if NMS_CYTHON_AVAILABLE:
    from .NMS import nms, soft_nms, soft_nms_39, soft_nms_merge
    __all__.extend(['nms', 'soft_nms', 'soft_nms_39', 'soft_nms_merge'])

if SHAPELY_NMS_AVAILABLE:
    from .NMS import delet_min_first, delet_min
    __all__.extend(['delet_min_first', 'delet_min'])

# 条件导入COCO API
if COCO_AVAILABLE:
    from .cocoapi import COCO
    __all__.extend(['COCO'])

    # 尝试导入其他COCO组件
    try:
        from .cocoapi import COCOeval
        __all__.append('COCOeval')
    except ImportError:
        pass

    try:
        from .cocoapi import mask
        __all__.append('mask')
    except ImportError:
        pass
