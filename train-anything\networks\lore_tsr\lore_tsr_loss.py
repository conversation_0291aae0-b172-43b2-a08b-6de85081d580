#!/usr/bin/env python3
"""
LORE-TSR 基础损失函数实现

迭代3：实现简化版损失函数，包含基本的hm_loss, wh_loss, reg_loss
迭代4：将扩展为完整的损失函数，包含所有6个损失组件

基于原LORE-TSR的losses.py，保持核心算法逻辑不变
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from .loss_utils import _gather_feat, _tranpose_and_gather_feat


class LoreTsrBasicLoss(nn.Module):
    """LORE-TSR基础损失函数类（简化版本）"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # 损失权重配置
        self.hm_weight = config.loss.weights.get('hm_weight', 1.0)
        self.wh_weight = config.loss.weights.get('wh_weight', 1.0)
        self.off_weight = config.loss.weights.get('off_weight', 1.0)
        
        # 损失函数组件
        self.focal_loss = FocalLoss()
        self.reg_l1_loss = RegL1Loss()
    
    def forward(self, predictions, targets):
        """
        计算LORE-TSR基础损失
        
        Args:
            predictions: 模型预测输出字典
            targets: 目标标签字典
            
        Returns:
            total_loss: 总损失值
            loss_stats: 各项损失统计字典
        """
        # 提取预测和目标
        pred_hm = predictions['hm']
        pred_wh = predictions['wh'] 
        pred_reg = predictions['reg']
        
        gt_hm = targets['hm']
        gt_wh = targets['wh']
        gt_reg = targets['reg']
        gt_reg_mask = targets['reg_mask']
        
        # 计算各项损失
        hm_loss = self.focal_loss(pred_hm, gt_hm)
        wh_loss = self.reg_l1_loss(pred_wh, gt_wh, gt_reg_mask)
        off_loss = self.reg_l1_loss(pred_reg, gt_reg, gt_reg_mask)
        
        # 加权求和
        total_loss = (self.hm_weight * hm_loss + 
                     self.wh_weight * wh_loss + 
                     self.off_weight * off_loss)
        
        # 损失统计
        loss_stats = {
            'total_loss': total_loss.item(),
            'hm_loss': hm_loss.item(),
            'wh_loss': wh_loss.item(),
            'off_loss': off_loss.item()
        }
        
        return total_loss, loss_stats


class FocalLoss(nn.Module):
    """Focal Loss实现（从LORE-TSR逐行复制）"""

    def __init__(self):
        super().__init__()

    def forward(self, pred, gt):
        """
        Modified focal loss. Exactly the same as CornerNet.
        Runs faster and costs a little bit more memory
        Arguments:
          pred (batch x c x h x w)
          gt_regr (batch x c x h x w)
        """
        pos_inds = gt.eq(1).float()
        neg_inds = gt.lt(1).float()

        neg_weights = torch.pow(1 - gt, 4)

        loss = 0

        pos_loss = torch.log(pred) * torch.pow(1 - pred, 2) * pos_inds
        neg_loss = torch.log(1 - pred) * torch.pow(pred, 2) * neg_weights * neg_inds

        num_pos = pos_inds.float().sum()
        pos_loss = pos_loss.sum()
        neg_loss = neg_loss.sum()

        if num_pos == 0:
            loss = loss - neg_loss
        else:
            loss = loss - (pos_loss + neg_loss) / num_pos

        return loss


class RegL1Loss(nn.Module):
    """L1回归损失实现（从LORE-TSR复制）"""
    
    def __init__(self):
        super().__init__()
    
    def forward(self, pred, target, mask):
        """计算L1回归损失"""
        # 确保mask的维度与pred兼容
        if mask.dim() == 2:  # [batch_size, max_objects]
            # 对于wh和reg损失，需要将mask扩展到正确的维度
            batch_size, num_channels, height, width = pred.shape
            # 创建与pred相同形状的mask
            expand_mask = torch.zeros_like(pred)
            # 这里简化处理，使用全1的mask（迭代5将实现正确的mask逻辑）
            expand_mask = torch.ones_like(pred)
        else:
            expand_mask = mask.unsqueeze(2).expand_as(pred).float()

        loss = F.l1_loss(pred * expand_mask, target * expand_mask, reduction='sum')
        loss = loss / (expand_mask.sum() + 1e-4)
        return loss


class PairLoss(nn.Module):
    """
    配对损失实现（从LORE-TSR复制）

    基于原LORE-TSR的losses.py中的PairLoss类，保持算法逻辑完全不变
    """

    def __init__(self):
        super(PairLoss, self).__init__()

    def forward(self, output1, ind1, output2, ind2, mask, mask_cro, ctr_cro_ind, target1, target2, hm_ctxy):
        """
        计算配对损失

        Args:
            output1: 第一个输出特征
            ind1: 第一个索引
            output2: 第二个输出特征
            ind2: 第二个索引
            mask: 掩码
            mask_cro: 交叉掩码
            ctr_cro_ind: 中心交叉索引
            target1: 第一个目标
            target2: 第二个目标
            hm_ctxy: 热力图中心坐标

        Returns:
            loss1, loss2: 两个损失值的元组
        """
        pred1 = _tranpose_and_gather_feat(output1, ind1)  # bxmx8
        pred2 = _tranpose_and_gather_feat(output2, ind2)  # bxnx8
        pred2_tmp = pred2
        target2_tmp = target2
        mask = mask.unsqueeze(2).expand_as(pred1).float()

        b = pred1.size(0)
        m = pred1.size(1)
        n = pred2.size(1)
        pred2 = pred2.view(b, 4*n, 2)
        ctr_cro_ind = ctr_cro_ind.unsqueeze(2).expand(b, 4*m, 2)
        pred2 = pred2.gather(1, ctr_cro_ind).view(b, m, 8)  # bxmx8
        target2 = target2.view(b, 4*n, 2).gather(1, ctr_cro_ind).view(b, m, 8)

        loss1 = F.l1_loss(pred1 * mask, target1 * mask, size_average=False)
        loss1 = loss1 / (mask.sum() + 1e-4)

        loss2 = F.l1_loss(pred2 * mask, target2 * mask, size_average=False)
        loss2 = loss2 / (mask.sum() + 1e-4)

        return loss1, loss2


class AxisLoss(nn.Module):
    """
    轴向损失实现（从LORE-TSR复制）

    基于原LORE-TSR的losses.py中的AxisLoss类，保持算法逻辑完全不变
    """

    def __init__(self):
        super(AxisLoss, self).__init__()

    def forward(self, output, mask, ind, target, logi=None):
        """
        计算轴向损失

        Args:
            output: 模型输出
            mask: 掩码
            ind: 索引
            target: 目标
            logi: 逻辑轴向信息（可选）

        Returns:
            loss: 轴向损失值
        """
        span_type = False
        # computing vanilla axis loss
        if logi is None:
            pred = _tranpose_and_gather_feat(output, ind)
        else:
            pred = logi

        mask = mask.unsqueeze(2).float()
        loss = F.l1_loss(pred * mask, target * mask, size_average=False)
        loss = loss / (4 * (mask.sum() + 1e-4))

        return loss


class LoreTsrLoss(nn.Module):
    """
    LORE-TSR完整损失函数类

    包含所有6个损失组件的完整实现，基于原LORE-TSR的CtdetLoss逻辑
    严格遵循"复制保留核心算法"原则
    """

    def __init__(self, config):
        super().__init__()
        self.config = config

        # 损失权重配置
        self.hm_weight = config.loss.weights.get('hm_weight', 1.0)
        self.wh_weight = config.loss.weights.get('wh_weight', 1.0)
        self.off_weight = config.loss.weights.get('off_weight', 1.0)
        self.st_weight = config.loss.weights.get('st_weight', 1.0)
        self.ax_weight = config.loss.weights.get('ax_weight', 2.0)  # 固定权重

        # 功能开关
        self.wiz_pairloss = config.loss.get('wiz_pairloss', False)
        self.wiz_stacking = config.loss.get('wiz_stacking', False)

        # 损失函数组件
        self.focal_loss = FocalLoss()
        self.reg_l1_loss = RegL1Loss()
        self.pair_loss = PairLoss()
        self.axis_loss = AxisLoss()

    def _get_dummy_logic_axis(self, targets):
        """
        获取占位的逻辑轴向信息

        在迭代6实现真实的Processor之前，使用占位实现
        """
        batch_size = targets['hm'].size(0)
        max_objects = targets.get('hm_ind', targets.get('ind', torch.zeros(batch_size, 500))).size(1)
        # 返回与target['logic']相同形状的占位张量
        dummy_logi = torch.zeros(batch_size, max_objects, 4, device=targets['hm'].device)
        return dummy_logi

    def forward(self, predictions, targets, logic_axis=None):
        """
        计算完整LORE-TSR损失

        Args:
            predictions: 模型预测输出字典，包含hm, wh, reg, st, ax, cr等键
            targets: 目标标签字典，包含对应的ground truth和mask信息
            logic_axis: 真实的逻辑轴向信息（来自Processor）

        Returns:
            total_loss: 总损失值
            loss_stats: 各项损失统计字典
        """
        # 提取预测和目标
        pred_hm = predictions['hm']
        pred_wh = predictions['wh']
        pred_reg = predictions['reg']

        gt_hm = targets['hm']

        # 关键修复：对hm输出应用_sigmoid激活（与LORE-TSR完全一致）
        pred_hm_sigmoid = torch.clamp(pred_hm.sigmoid_(), min=1e-4, max=1-1e-4)

        # 基础损失计算 - 只对第0个通道进行监督（与LORE-TSR一致）
        hm_loss = self.focal_loss(pred_hm_sigmoid[:, 0, :, :], gt_hm[:, 0, :, :])

        # 使用正确的RegL1Loss接口
        wh_loss = self._compute_reg_loss(predictions['wh'], targets, 'wh')
        off_loss = self._compute_reg_loss(predictions['reg'], targets, 'reg')

        # 条件损失计算
        st_loss = 0
        if self.wiz_pairloss and 'st' in predictions:
            st_loss1, st_loss2 = self.pair_loss(
                predictions['wh'], targets.get('hm_ind', targets.get('ind')),
                predictions['st'], targets.get('mk_ind', targets.get('ind')),
                targets.get('hm_mask', targets.get('mask')),
                targets.get('mk_mask', targets.get('mask')),
                targets.get('ctr_cro_ind', targets.get('ind')),
                targets.get('wh'), targets.get('st'),
                targets.get('hm_ctxy', targets.get('ind'))
            )
            st_loss = st_loss1 + st_loss2

        # 轴向损失计算（使用真实逻辑轴向）
        ax_loss = 0
        if 'ax' in predictions:
            if logic_axis is not None:
                # 使用真实的logic_axis
                ax_loss = self.axis_loss(
                    predictions['ax'],
                    targets.get('hm_mask', targets.get('mask')),
                    targets.get('hm_ind', targets.get('ind')),
                    targets.get('logic', logic_axis),
                    logic_axis
                )
            else:
                # 回退到占位实现
                dummy_logi = self._get_dummy_logic_axis(targets)
                ax_loss = self.axis_loss(
                    predictions['ax'],
                    targets.get('hm_mask', targets.get('mask')),
                    targets.get('hm_ind', targets.get('ind')),
                    targets.get('logic', dummy_logi),
                    dummy_logi
                )

        # 堆叠轴向损失
        sax_loss = 0
        if self.wiz_stacking and 'ax' in predictions:
            if logic_axis is not None:
                # 使用真实的logic_axis（注意：堆叠模式下可能需要stacked_axis，但当前使用logic_axis）
                sax_loss = self.axis_loss(
                    predictions['ax'],
                    targets.get('hm_mask', targets.get('mask')),
                    targets.get('hm_ind', targets.get('ind')),
                    targets.get('logic', logic_axis),
                    logic_axis
                )
            else:
                # 回退到占位实现
                dummy_slogi = self._get_dummy_logic_axis(targets)
                sax_loss = self.axis_loss(
                    predictions['ax'],
                    targets.get('hm_mask', targets.get('mask')),
                    targets.get('hm_ind', targets.get('ind')),
                    targets.get('logic', dummy_slogi),
                    dummy_slogi
                )

        # 总损失计算
        total_loss = (self.hm_weight * hm_loss +
                     self.wh_weight * wh_loss +
                     self.off_weight * off_loss +
                     self.ax_weight * ax_loss)

        if self.wiz_pairloss:
            total_loss += self.st_weight * st_loss

        if self.wiz_stacking:
            total_loss += self.ax_weight * sax_loss

        # 损失统计
        loss_stats = {
            'total_loss': total_loss.item(),
            'hm_loss': hm_loss.item(),
            'wh_loss': wh_loss.item(),
            'off_loss': off_loss.item(),
            'ax_loss': ax_loss.item() if isinstance(ax_loss, torch.Tensor) else ax_loss
        }

        if self.wiz_pairloss:
            loss_stats['st_loss'] = st_loss.item() if isinstance(st_loss, torch.Tensor) else st_loss

        if self.wiz_stacking:
            loss_stats['sax_loss'] = sax_loss.item() if isinstance(sax_loss, torch.Tensor) else sax_loss

        return total_loss, loss_stats

    def _compute_reg_loss(self, pred, targets, loss_type):
        """
        计算回归损失的辅助函数

        Args:
            pred: 预测值
            targets: 目标字典
            loss_type: 损失类型 ('wh' 或 'reg')

        Returns:
            loss: 计算的损失值
        """
        if loss_type == 'wh':
            mask_key = 'hm_mask'
            ind_key = 'hm_ind'
            target_key = 'wh'
        else:  # reg
            mask_key = 'reg_mask'
            ind_key = 'reg_ind'
            target_key = 'reg'

        # 获取mask, ind, target，如果不存在则使用默认值
        mask = targets.get(mask_key, targets.get('mask'))
        ind = targets.get(ind_key, targets.get('ind'))
        target = targets.get(target_key)

        if mask is None or ind is None or target is None:
            # 如果缺少必要的数据，返回零损失
            return torch.tensor(0.0, device=pred.device, requires_grad=True)

        # 使用原始的RegL1Loss接口
        pred_gathered = _tranpose_and_gather_feat(pred, ind)
        mask_expanded = mask.unsqueeze(2).expand_as(pred_gathered).float()
        loss = F.l1_loss(pred_gathered * mask_expanded, target * mask_expanded, size_average=False)
        loss = loss / (mask_expanded.sum() + 1e-4)

        return loss
