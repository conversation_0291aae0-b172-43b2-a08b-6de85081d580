#!/usr/bin/env python3
"""
LORE-TSR 迁移项目 - 步骤5.3验证测试脚本

迭代5步骤5.3：核心数据处理pipeline验证
测试完整的LORE-TSR数据处理流程，验证与原项目的数值一致性

验证内容：
1. 图像预处理逻辑测试
2. 随机数据增强测试
3. 仿射变换功能测试
4. 颜色增强和后处理测试
5. 完整pipeline端到端测试
"""

import sys
import os
import torch
import numpy as np
import cv2
import traceback
from pathlib import Path
from omegaconf import OmegaConf

# 添加train-anything到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def create_test_config():
    """创建测试配置"""
    config = OmegaConf.create({
        'data': {
            'dataset': {
                'data_root': r'D:\workspace\datasets\cf_train_clean\wired_tables_reorganized\TabRecSet_TableLabelMe_fix\chinese',
                'debug': True,
                'max_samples': 3  # 限制样本数量以加快测试
            },
            'processing': {
                'image_size': [768, 768],  # LORE-TSR标准尺寸
                'down_ratio': 4,
                'max_objs': 500,
                'gaussian_iou': 0.7,
                'upper_left': False,
                'keep_res': False,
                'pad': 31,
                'not_rand_crop': True
            },
            'augmentation': {
                'scale': 0.4,
                'shift': 0.1,
                'rotate': 0,
                'flip': 0.5,
                'no_color_aug': False
            }
        },
        'model': {
            'heads': {
                'hm': 1  # 单类别
            }
        }
    })
    return config

def test_image_preprocessing():
    """测试图像预处理逻辑"""
    print("=" * 60)
    print("测试1: 图像预处理逻辑测试")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        
        config = create_test_config()
        dataset = LoreTsrDataset(config, mode='train')
        
        # 创建测试图像
        test_img = np.random.randint(0, 255, (400, 600, 3), dtype=np.uint8)
        
        # 测试图像预处理
        c, s, input_h, input_w = dataset._apply_image_preprocessing(test_img)
        
        print(f"✅ 图像预处理完成")
        print(f"   原始图像形状: {test_img.shape}")
        print(f"   中心点: {c}")
        print(f"   缩放因子: {s}")
        print(f"   输入尺寸: {input_h}x{input_w}")
        
        # 验证结果
        assert len(c) == 2, f"中心点维度错误: {len(c)}"
        assert isinstance(s, (int, float, np.number)), f"缩放因子类型错误: {type(s)}"
        assert input_h == 768 and input_w == 768, f"输入尺寸错误: {input_h}x{input_w}"
        
        print("✅ 图像预处理逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 图像预处理测试失败: {e}")
        traceback.print_exc()
        return False

def test_random_augmentation():
    """测试随机数据增强"""
    print("\n" + "=" * 60)
    print("测试2: 随机数据增强测试")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        
        config = create_test_config()
        dataset = LoreTsrDataset(config, mode='train')
        
        # 测试参数
        c_orig = np.array([300.0, 200.0], dtype=np.float32)
        s_orig = 400.0
        img_shape = (400, 600)
        
        # 测试训练模式增强
        c_train, s_train, rot_train = dataset._apply_random_augmentation(
            c_orig.copy(), s_orig, img_shape, is_train=True)
        
        print(f"✅ 训练模式数据增强完成")
        print(f"   原始中心: {c_orig}, 增强后中心: {c_train}")
        print(f"   原始缩放: {s_orig}, 增强后缩放: {s_train}")
        print(f"   旋转角度: {rot_train}")
        
        # 测试验证模式（应该无增强）
        c_val, s_val, rot_val = dataset._apply_random_augmentation(
            c_orig.copy(), s_orig, img_shape, is_train=False)
        
        print(f"✅ 验证模式数据增强完成")
        print(f"   验证模式中心: {c_val}")
        print(f"   验证模式缩放: {s_val}")
        print(f"   验证模式旋转: {rot_val}")
        
        # 验证结果
        assert len(c_train) == 2, f"增强后中心点维度错误: {len(c_train)}"
        assert isinstance(s_train, (int, float, np.number)), f"增强后缩放类型错误: {type(s_train)}"
        assert isinstance(rot_train, (int, np.integer)), f"旋转角度类型错误: {type(rot_train)}"
        
        print("✅ 随机数据增强测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 随机数据增强测试失败: {e}")
        traceback.print_exc()
        return False

def test_affine_transform():
    """测试仿射变换功能"""
    print("\n" + "=" * 60)
    print("测试3: 仿射变换功能测试")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        
        config = create_test_config()
        dataset = LoreTsrDataset(config, mode='train')
        
        # 创建测试图像
        test_img = np.random.randint(0, 255, (400, 600, 3), dtype=np.uint8)
        c = np.array([300.0, 200.0], dtype=np.float32)
        s = 400.0
        rot = 0
        input_h, input_w = 768, 768
        
        # 测试仿射变换
        transformed_img, trans_input, trans_output = dataset._apply_affine_transform(
            test_img, c, s, rot, input_h, input_w)
        
        print(f"✅ 仿射变换完成")
        print(f"   原始图像形状: {test_img.shape}")
        print(f"   变换后图像形状: {transformed_img.shape}")
        print(f"   输入变换矩阵形状: {trans_input.shape}")
        print(f"   输出变换矩阵形状: {trans_output.shape}")
        
        # 验证结果
        assert transformed_img.shape == (input_h, input_w, 3), f"变换后图像形状错误: {transformed_img.shape}"
        assert trans_input.shape == (2, 3), f"输入变换矩阵形状错误: {trans_input.shape}"
        assert trans_output.shape == (2, 3), f"输出变换矩阵形状错误: {trans_output.shape}"
        
        print("✅ 仿射变换功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 仿射变换测试失败: {e}")
        traceback.print_exc()
        return False

def test_image_postprocessing():
    """测试图像后处理"""
    print("\n" + "=" * 60)
    print("测试4: 图像后处理测试")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        
        config = create_test_config()
        dataset = LoreTsrDataset(config, mode='train')
        
        # 创建测试图像
        test_img = np.random.randint(0, 255, (768, 768, 3), dtype=np.uint8)
        
        # 测试后处理
        processed_img = dataset._apply_image_postprocessing(test_img, is_train=True)
        
        print(f"✅ 图像后处理完成")
        print(f"   原始图像形状: {test_img.shape}, 数据类型: {test_img.dtype}")
        print(f"   处理后图像形状: {processed_img.shape}, 数据类型: {processed_img.dtype}")
        print(f"   处理后数值范围: [{processed_img.min():.3f}, {processed_img.max():.3f}]")
        
        # 验证结果
        assert isinstance(processed_img, torch.Tensor), f"处理后图像类型错误: {type(processed_img)}"
        assert processed_img.shape == (3, 768, 768), f"处理后图像形状错误: {processed_img.shape}"
        assert processed_img.dtype == torch.float32, f"处理后图像数据类型错误: {processed_img.dtype}"
        
        print("✅ 图像后处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 图像后处理测试失败: {e}")
        traceback.print_exc()
        return False

def test_complete_pipeline():
    """测试完整pipeline"""
    print("\n" + "=" * 60)
    print("测试5: 完整pipeline端到端测试")
    print("=" * 60)
    
    try:
        from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
        
        config = create_test_config()
        dataset = LoreTsrDataset(config, mode='train')
        
        if len(dataset) > 0:
            # 测试获取样本
            sample = dataset[0]
            
            print(f"✅ 完整pipeline执行成功")
            print(f"   样本结构: {list(sample.keys())}")
            
            # 验证关键字段
            required_keys = ['input', 'image_id', 'meta']
            for key in required_keys:
                assert key in sample, f"样本缺少键: {key}"
            
            # 验证输入图像
            input_img = sample['input']
            assert isinstance(input_img, torch.Tensor), f"输入图像类型错误: {type(input_img)}"
            assert input_img.shape == (3, 768, 768), f"输入图像形状错误: {input_img.shape}"
            print(f"   输入图像形状: {input_img.shape}")
            print(f"   输入图像数值范围: [{input_img.min():.3f}, {input_img.max():.3f}]")
            
            # 验证元信息
            meta = sample['meta']
            meta_keys = ['c', 's', 'rot', 'trans_input', 'trans_output', 'input_size', 'output_size']
            for key in meta_keys:
                assert key in meta, f"元信息缺少键: {key}"
            print(f"   元信息键: {list(meta.keys())}")
            
            # 验证目标信息
            target_keys = ['hm', 'wh', 'reg', 'reg_mask', 'ind', 'num_objs']
            for key in target_keys:
                assert key in sample, f"样本缺少目标键: {key}"
            print(f"   目标键: {[key for key in sample.keys() if key in target_keys]}")
            
            print("✅ 完整pipeline端到端测试通过")
            return True
        else:
            print("⚠️  数据集为空，跳过完整pipeline测试")
            return True
        
    except Exception as e:
        print(f"❌ 完整pipeline测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("LORE-TSR 迁移项目 - 步骤5.3验证测试")
    print("测试目标: 验证核心数据处理pipeline的正确性")
    print("迁移策略: 复制保留核心算法")
    
    # 执行所有测试
    tests = [
        test_image_preprocessing,
        test_random_augmentation,
        test_affine_transform,
        test_image_postprocessing,
        test_complete_pipeline
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            results.append(False)
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！步骤5.3验证成功")
        print("✅ LORE-TSR核心数据处理pipeline实现完成")
        return True
    else:
        print("❌ 部分测试失败，需要检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
