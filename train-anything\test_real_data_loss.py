#!/usr/bin/env python3
"""
测试真实数据上的损失函数
"""

from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
from networks.lore_tsr.lore_tsr_model import create_lore_tsr_model
from networks.lore_tsr.lore_tsr_loss import LoreTsrLoss
from networks.lore_tsr.processor import Processor
from omegaconf import OmegaConf
import torch
from torch.utils.data import DataLoader

def test_real_data_loss():
    config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml')
    
    # 临时修改配置以使用指定的数据路径
    config.data.paths.train_data_dir = ['D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese']

    # 使用真实数据集
    dataset = LoreTsrDataset(config=config, mode='train')
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False)
    
    model = create_lore_tsr_model(config)
    loss_fn = LoreTsrLoss(config)
    processor = Processor(config)
    
    print('✅ 真实数据集加载成功')
    
    # 获取一个真实样本
    batch = next(iter(dataloader))

    print('真实数据形状:')
    print(f'  - 数据键: {list(batch.keys())}')
    print(f'  - input: {batch["input"].shape}')

    # 检查targets的结构
    if 'targets' in batch:
        targets = batch['targets']
        print(f'  - targets keys: {list(targets.keys())}')
        if 'hm' in targets:
            hm_min = targets['hm'].min().item()
            hm_max = targets['hm'].max().item()
            print(f'  - targets hm: {targets["hm"].shape}')
            print(f'  - targets hm range: [{hm_min:.4f}, {hm_max:.4f}]')
    else:
        print('  - 没有targets键，使用其他键作为targets')
        # 尝试直接使用batch作为targets
        targets = batch
    
    # 模型前向传播
    predictions = model(batch['input'])
    if isinstance(predictions, list):
        predictions = predictions[0]
    
    print(f'模型输出形状: {list(predictions.keys())}')
    
    pred_hm_min = predictions['hm'].min().item()
    pred_hm_max = predictions['hm'].max().item()
    print(f'  - hm range: [{pred_hm_min:.4f}, {pred_hm_max:.4f}]')
    
    # Processor调用
    logic_axis = processor(predictions, targets)
    print(f'✅ Processor调用成功: {logic_axis.shape}')

    # 损失计算
    total_loss, loss_stats = loss_fn(predictions, targets, logic_axis)
    print(f'✅ 真实数据损失: 总损失{total_loss.item():.4f}')
    
    hm_loss = loss_stats.get('hm_loss', 0)
    wh_loss = loss_stats.get('wh_loss', 0)
    off_loss = loss_stats.get('off_loss', 0)
    ax_loss = loss_stats.get('ax_loss', 0)
    
    print(f'  - hm_loss: {hm_loss:.4f}')
    print(f'  - wh_loss: {wh_loss:.4f}')
    print(f'  - off_loss: {off_loss:.4f}')
    print(f'  - ax_loss: {ax_loss:.4f}')
    
    print('🎉 真实数据验证通过')

if __name__ == "__main__":
    test_real_data_loss()
