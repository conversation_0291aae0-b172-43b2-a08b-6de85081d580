{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "from pycocotools.coco import COCO\n", "from pycocotools.cocoeval import COCOeval\n", "import numpy as np\n", "import skimage.io as io\n", "import pylab\n", "pylab.rcParams['figure.figsize'] = (10.0, 8.0)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running demo for *bbox* results.\n"]}], "source": ["annType = ['segm','bbox','keypoints']\n", "annType = annType[1]      #specify type here\n", "prefix = 'person_keypoints' if annType=='keypoints' else 'instances'\n", "print 'Running demo for *%s* results.'%(annType)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loading annotations into memory...\n", "Done (t=8.01s)\n", "creating index...\n", "index created!\n"]}], "source": ["#initialize COCO ground truth api\n", "dataDir='../'\n", "dataType='val2014'\n", "annFile = '%s/annotations/%s_%s.json'%(dataDir,prefix,dataType)\n", "cocoGt=COCO(annFile)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading and preparing results...     \n", "DONE (t=0.05s)\n", "creating index...\n", "index created!\n"]}], "source": ["#initialize COCO detections api\n", "resFile='%s/results/%s_%s_fake%s100_results.json'\n", "resFile = resFile%(dataDir, prefix, dataType, annType)\n", "cocoDt=cocoGt.loadRes(resFile)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [], "source": ["imgIds=sorted(cocoGt.getImgIds())\n", "imgIds=imgIds[0:100]\n", "imgId = imgIds[np.random.randint(100)]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running per image evaluation...      \n", "DONE (t=0.46s).\n", "Accumulating evaluation results...   \n", "DONE (t=0.38s).\n", " Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.505\n", " Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.697\n", " Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.573\n", " Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.586\n", " Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.519\n", " Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.501\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.387\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.594\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.595\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.640\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.566\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.564\n"]}], "source": ["# running evaluation\n", "cocoEval = COCOeval(cocoGt,cocoDt,annType)\n", "cocoEval.params.imgIds  = imgIds\n", "cocoEval.evaluate()\n", "cocoEval.accumulate()\n", "cocoEval.summarize()"]}], "metadata": {"kernelspec": {"display_name": "Python 2", "language": "python", "name": "python2"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.10"}}, "nbformat": 4, "nbformat_minor": 0}