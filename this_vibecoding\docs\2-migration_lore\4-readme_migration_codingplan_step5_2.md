# LORE-TSR 迁移项目 - 迭代5步骤5.2渐进式小步迁移计划

## 📋 文档信息
- **迁移阶段**: 迭代5 - 数据集适配器实现
- **当前步骤**: 步骤5.2 - 数据集基础框架（继承TableDataset）
- **制定日期**: 2025-07-20
- **基于文档**: 
  - PRD: @`this_vibecoding/docs/2-migration_lore/2-readme_migration_lore_prdplan.md`
  - LLD: @`this_vibecoding/docs/2-migration_lore/3-readme_migration_lore_lld_iter5.md`
  - 步骤5.1报告: @`this_vibecoding/docs/2-migration_lore/migration_reports/step_5_1_report.md`

## 🎯 迭代5.2核心目标

### 总体目标
建立LoreTsrDataset基础框架，实现WTW格式加载，为后续完整LORE-TSR数据处理pipeline奠定基础。

### 核心原则
- **继承TableDataset**: 充分利用train-anything现有的数据加载基础设施
- **格式等价转换**: 利用WTW格式与COCO格式的等价性，实现直接转换
- **渐进式实现**: 先实现基础框架，为步骤5.3的完整pipeline做准备
- **保持兼容性**: 确保与train-anything框架完全兼容

### 依赖关系
- **依赖步骤5.1**: 使用已完成的`lore_image_utils.py`工具函数
- **为步骤5.3准备**: 提供数据格式转换和基础加载能力

## 📊 动态迁移蓝图

### 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `src/lib/utils/image.py` | `modules/utils/lore_tsr/lore_image_utils.py` | 复制保留：逐行复制核心算法 | 迭代5.1 | 简单 | **✅ 已完成** |
| `src/lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **重构适配：数据集适配器** | **迭代5.2** | **复杂** | **进行中** |
| `src/lib/datasets/sample/ctdet.py` | `my_datasets/table_structure_recognition/lore_tsr_transforms.py` | 复制保留：完整数据处理pipeline | 迭代5.3 | **复杂** | `未开始` |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配：转换为OmegaConf YAML格式 | 迭代1 | **复杂** | `已完成` |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配：适配accelerate框架 | 迭代1,3 | **复杂** | `已完成` |
| `src/lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留：模型工厂函数 | 迭代2 | **复杂** | `已完成` |
| `src/lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留：逐行复制保持精度 | 迭代4 | 简单 | `已完成` |
| `src/lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留：Processor组件 | 迭代6 | **复杂** | `未开始` |
| `src/lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留：Transformer实现 | 迭代6 | **复杂** | `未开始` |

### 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代5.2 - 数据集基础框架

    subgraph "Source: LORE-TSR数据处理逻辑"
        direction LR
        S1["table_mid.py<br/>COCO数据加载"]
        S2["WTW分布式标注<br/>格式等价性"]
        S3["LORE内部格式<br/>segmentation + logic_axis"]
    end

    subgraph "Target: train-anything TableDataset继承"
        direction LR
        T1["TableDataset<br/>基类功能"]
        T2["LoreTsrDataset<br/>继承实现"]
        T3["格式转换逻辑<br/>WTW → LORE"]
    end

    subgraph "核心转换逻辑"
        direction TB
        C1["WTW bbox.p1,p2,p3,p4<br/>→ segmentation展平坐标"]
        C2["WTW lloc.start_row/col<br/>→ logic_axis数组"]
        C3["禁用TableTransforms<br/>为LORE-TSR pipeline准备"]
    end

    %% 迁移映射 - 重构适配策略
    S1 -- "Refactor & Adapt" --> T2
    S2 -- "Direct Conversion" --> T3
    S3 -- "Format Equivalence" --> T3

    %% 继承关系
    T1 -.-> T2

    %% 转换流程
    T2 --> C1
    T2 --> C2
    T2 --> C3

    %% 依赖关系
    T2 -.-> L1["lore_image_utils.py<br/>（步骤5.1已完成）"]
    T2 -.-> L2["为步骤5.3预留<br/>完整pipeline接口"]
```

## 🏗️ 目标目录结构树

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                      # [已完成]
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                         # [已完成]
├── networks/lore_tsr/
│   ├── __init__.py                               # [已完成]
│   ├── lore_tsr_model.py                         # [已完成]
│   ├── lore_tsr_loss.py                          # [已完成]
│   ├── backbones/                                # [已完成]
│   └── heads/                                    # [已完成]
├── my_datasets/table_structure_recognition/      # [当前创建]
│   ├── __init__.py                               # [步骤5.2.1创建]
│   └── lore_tsr_dataset.py                       # [步骤5.2.2-5.2.4创建]
├── modules/utils/lore_tsr/                       # [已完成]
│   ├── __init__.py                               # [已完成]
│   └── lore_image_utils.py                       # [已完成]
└── external/lore_tsr/                            # [待创建]
    ├── DCNv2/                                    # [待创建]
    ├── NMS/                                      # [待创建]
    └── cocoapi/                                  # [待创建]
```

## 🔄 渐进式小步迁移计划

### 步骤5.2.1: 创建数据集目录结构和基础类框架

**当前迭代**: 迭代5.2 - 数据集基础框架  
**步骤目标**: 建立数据集目录结构，创建继承TableDataset的基础类框架

**影响文件**:
- 创建: `my_datasets/table_structure_recognition/__init__.py`
- 创建: `my_datasets/table_structure_recognition/lore_tsr_dataset.py` (基础框架)

**具体操作**:
1. 创建数据集目录结构
2. 实现LoreTsrDataset类的基础框架，继承TableDataset
3. 设置基础的类属性和导入语句
4. 确保类可以正确实例化

**受影响的现有模块**: 无，纯增量添加

**复用已有代码**: 继承train-anything的TableDataset基类

**如何验证**:
```bash
# 验证目录创建成功
ls -la my_datasets/table_structure_recognition/

# 验证基础类可正确导入和实例化
cd train-anything
python -c "
from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
print('LoreTsrDataset类导入成功')
print('基础框架创建完成')
"
```

### 步骤5.2.2: 实现TableDataset继承和初始化逻辑

**当前迭代**: 迭代5.2 - 数据集基础框架  
**步骤目标**: 实现完整的__init__方法，正确继承TableDataset，设置LORE-TSR特有参数

**影响文件**:
- 扩展: `my_datasets/table_structure_recognition/lore_tsr_dataset.py` (初始化部分)

**具体操作**:
1. 实现完整的`__init__`方法，正确调用父类TableDataset初始化
2. 设置LORE-TSR特有的参数：
   - `max_objs = 500` (最大目标数量)
   - `max_pairs = 900` (最大配对数量)  
   - `max_cors = 1200` (最大角点数量)
   - `num_classes = 2` (类别数量)
   - LORE-TSR的mean和std参数
3. 禁用父类的transforms (`apply_transforms=False`)
4. 为LORE-TSR数据处理pipeline做准备

**受影响的现有模块**: 无，纯增量添加

**复用已有代码**: 使用TableDataset的数据加载基础设施

**如何验证**:
```bash
# 验证数据集初始化无错误
cd train-anything
python -c "
from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
from omegaconf import DictConfig

# 创建测试配置
config = DictConfig({
    'data': {
        'paths': {
            'train_data_dir': ['D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese'],
            'val_data_dir': ['D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese']
        },
        'processing': {
            'image_size': [768, 768],
            'down_ratio': 4
        },
        'targets': {
            'max_objs': 500,
            'num_classes': 2
        }
    }
})

dataset = LoreTsrDataset(config, mode='train')
print(f'数据集初始化成功，样本数量: {len(dataset)}')
print(f'LORE-TSR参数设置正确: max_objs={dataset.max_objs}')
"
```

### 步骤5.2.3: 实现WTW到LORE格式转换核心逻辑

**当前迭代**: 迭代5.2 - 数据集基础框架
**步骤目标**: 实现`_convert_wtw_to_lore_format`方法，完成WTW格式到LORE内部格式的转换

**影响文件**:
- 扩展: `my_datasets/table_structure_recognition/lore_tsr_dataset.py` (格式转换部分)

**具体操作**:
1. 实现`_convert_wtw_to_lore_format`方法，核心转换逻辑：
   - WTW的`bbox.p1,p2,p3,p4`四点坐标 → LORE的`segmentation`展平坐标
   - WTW的`lloc.start_row/col,end_row/col` → LORE的`logic_axis`数组
   - 设置固定的`category_id=1`（前景类）
2. 利用格式等价性，确保转换的数值精度
3. 处理边界情况和异常数据
4. 添加详细的转换验证逻辑

**受影响的现有模块**: 无，纯增量添加

**复用已有代码**: 利用WTW与COCO格式的等价性

**如何验证**:
```bash
# 验证格式转换的正确性
cd train-anything
python -c "
from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
from omegaconf import DictConfig
import json

# 创建测试配置
config = DictConfig({
    'data': {
        'paths': {
            'train_data_dir': ['D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese']
        },
        'processing': {'image_size': [768, 768]},
        'targets': {'max_objs': 500, 'num_classes': 2}
    }
})

dataset = LoreTsrDataset(config, mode='train')

# 测试格式转换
if len(dataset) > 0:
    sample_annotation = dataset.annotations[0]
    lore_anns = dataset._convert_wtw_to_lore_format(sample_annotation)
    print(f'WTW格式转换成功，转换后标注数量: {len(lore_anns)}')
    if lore_anns:
        print(f'第一个标注格式: {list(lore_anns[0].keys())}')
        print('格式转换验证通过')
else:
    print('数据集为空，跳过转换测试')
"
```

### 步骤5.2.4: 实现基础数据加载和验证测试

**当前迭代**: 迭代5.2 - 数据集基础框架
**步骤目标**: 实现基础的`__getitem__`方法，创建完整验证测试，确保数据集功能正常

**影响文件**:
- 扩展: `my_datasets/table_structure_recognition/lore_tsr_dataset.py` (数据加载部分)
- 创建: `test_lore_tsr_step5_2.py` (验证测试脚本)
- 创建: `test_reports/step_5_2_verification_report.md` (验证报告)

**具体操作**:
1. 实现基础的`__getitem__`方法：
   - 调用父类获取WTW格式数据
   - 应用格式转换得到LORE格式标注
   - 返回基础的样本数据（为步骤5.3的完整pipeline预留接口）
2. 创建全面的验证测试脚本：
   - 测试数据集初始化
   - 测试格式转换正确性
   - 测试数据加载功能
   - 测试与train-anything框架的兼容性
3. 生成详细的验证报告

**受影响的现有模块**: 无，纯增量添加

**复用已有代码**: 使用TableDataset的图像加载和基础设施

**如何验证**:
```bash
# 运行完整验证测试
cd train-anything
python test_lore_tsr_step5_2.py

# 查看验证报告
cat test_reports/step_5_2_verification_report.md

# 验证数据加载功能
python -c "
from my_datasets.table_structure_recognition.lore_tsr_dataset import LoreTsrDataset
from omegaconf import DictConfig

config = DictConfig({
    'data': {
        'paths': {
            'train_data_dir': ['D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese']
        },
        'processing': {'image_size': [768, 768], 'down_ratio': 4},
        'targets': {'max_objs': 500, 'num_classes': 2}
    }
})

dataset = LoreTsrDataset(config, mode='train')
if len(dataset) > 0:
    sample = dataset[0]
    print(f'样本加载成功，包含键: {list(sample.keys())}')
    print(f'图像形状: {sample[\"image\"].shape}')
    print(f'LORE标注数量: {len(sample[\"lore_annotations\"])}')
    print('数据加载验证通过')
else:
    print('数据集为空，跳过加载测试')
"
```

## 🎯 验收标准

### 功能验收
1. **继承正确性**: LoreTsrDataset正确继承TableDataset，所有基础功能正常
2. **格式转换**: WTW到LORE格式转换完全正确，数值精度一致
3. **数据加载**: 基础数据加载功能正常，返回正确的样本格式
4. **参数设置**: 所有LORE-TSR特有参数正确设置

### 兼容性验收
1. **框架兼容**: 与train-anything现有功能完全兼容，无冲突
2. **配置兼容**: 正确使用OmegaConf配置系统
3. **接口预留**: 为步骤5.3的完整pipeline预留合理接口

### 质量验收
1. **代码质量**: 代码结构清晰，注释完整，符合Python规范
2. **测试覆盖**: 验证测试覆盖所有核心功能
3. **错误处理**: 合理处理边界情况和异常数据

## 🚨 风险控制

### 技术风险
1. **格式转换风险**: 通过详细的数值对比验证确保转换正确性
2. **继承兼容风险**: 仔细研究TableDataset接口，确保正确继承
3. **配置映射风险**: 确保LORE-TSR参数正确映射到OmegaConf配置

### 集成风险
1. **数据路径风险**: 确保WTW数据路径配置正确
2. **内存使用风险**: 合理控制数据加载的内存使用
3. **性能风险**: 确保格式转换不影响数据加载性能

### 项目风险
1. **进度风险**: 严格按照小步迭代执行，每步都有明确验证标准
2. **质量风险**: 通过自动化测试和详细验证报告确保质量

## 📝 后续迭代接口预留

### 为迭代5.3预留的接口
```python
# 完整数据处理pipeline接口
def _apply_lore_pipeline(self, image, lore_anns):
    """为步骤5.3预留：应用完整的LORE-TSR数据处理pipeline"""
    # 步骤5.3将实现完整的ctdet逻辑
    pass

def _prepare_lore_targets(self, lore_anns, image_info):
    """为步骤5.4预留：准备LORE-TSR训练目标"""
    # 步骤5.4将实现完整的目标生成
    pass
```

### 为迭代5.4预留的接口
```python
# 目标生成接口
def get_target_generation_params(self):
    """返回目标生成所需的参数"""
    return {
        'max_objs': self.max_objs,
        'max_pairs': self.max_pairs,
        'max_cors': self.max_cors,
        'num_classes': self.num_classes
    }
```

## 🔗 与已完成迭代的集成

### 使用步骤5.1的工具函数
```python
# 在LoreTsrDataset中使用已完成的工具函数
from modules.utils.lore_tsr.lore_image_utils import (
    get_affine_transform,
    affine_transform,
    draw_umich_gaussian,
    gaussian_radius
)

# 这些函数将在步骤5.3中被大量使用
```

### 与已完成配置系统集成
```python
# 使用迭代1完成的配置系统
def __init__(self, config, mode='train'):
    # 从config中获取LORE-TSR参数
    self.max_objs = config.data.targets.max_objs
    self.input_h, self.input_w = config.data.processing.image_size
    self.down_ratio = config.data.processing.down_ratio
```

---

**文档版本**: v1.0
**创建日期**: 2025-07-20
**适用迭代**: 迭代5.2 - 数据集基础框架
**依赖迭代**: 迭代1-4（已完成），迭代5.1（已完成）
**预计工期**: 4个渐进式小步，每步0.5-1天，总计2-3个工作日
**核心交付**: 1个数据集类 + 格式转换逻辑 + 完整验证测试
**成功关键**: 正确继承TableDataset + 格式转换准确性 + 为后续步骤预留接口
