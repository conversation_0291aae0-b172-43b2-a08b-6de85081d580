#!/usr/bin/env python3
"""
LORE-TSR 权重加载器

迭代8步骤8.2：权重加载器和验证器
负责自动检测权重格式并加载，提供统一的权重加载接口
"""

import os
import tempfile
import torch
import logging
from typing import Dict, Optional, List, Union, Tuple, Any
from pathlib import Path

from .weight_utils import (
    detect_checkpoint_format,
    validate_weight_file,
    get_weight_file_info
)
from .weight_converter import LoreTsrWeightConverter

logger = logging.getLogger(__name__)


class WeightFormatDetector:
    """权重格式检测器"""
    
    @staticmethod
    def detect_format(path: str) -> str:
        """检测权重格式"""
        return detect_checkpoint_format(path)
    
    @staticmethod
    def is_lore_format(path: str) -> bool:
        """检查是否为LORE-TSR格式"""
        return detect_checkpoint_format(path) == 'lore'
    
    @staticmethod
    def is_train_anything_format(path: str) -> bool:
        """检查是否为train-anything格式"""
        return detect_checkpoint_format(path) == 'train_anything'
    
    @staticmethod
    def get_format_info(path: str) -> Dict:
        """获取格式信息"""
        return get_weight_file_info(path)


class LoreTsrWeightLoader:
    """
    LORE-TSR权重加载器
    
    集成到train-anything训练循环中，支持自动检测权重格式并加载，
    提供统一的权重加载接口，隐藏格式差异
    """
    
    def __init__(self, config: Dict, logger_obj: Any = None, accelerator: Any = None):
        """
        初始化权重加载器
        
        Args:
            config (dict): 配置对象
            logger_obj: 日志记录器
            accelerator: accelerate对象
        """
        self.config = config
        self.logger = logger_obj or logger
        self.accelerator = accelerator
        self.converter = LoreTsrWeightConverter()
        self.detector = WeightFormatDetector()
        
        # 从配置中获取权重路径
        self.model_path = self._get_config_value('model.load_model', '')
        self.processor_path = self._get_config_value('model.load_processor', '')
        self.checkpoint_path = self._get_config_value('checkpoint.resume.from_checkpoint', '')
        
        # 权重兼容性配置
        self.load_mode = self._get_config_value('model.weight_compatibility.load_mode', 'auto')
        self.strict_mode = self._get_config_value('model.weight_compatibility.strict_mode', False)
        
        logger.info(f"权重加载器初始化完成，加载模式: {self.load_mode}")
    
    def _get_config_value(self, key_path: str, default: Any = None) -> Any:
        """从配置中获取值"""
        try:
            keys = key_path.split('.')
            value = self.config
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def load_checkpoint_state(self) -> Tuple[int, int, Optional[str], Optional[Dict], 
                                          Optional[Dict], Optional[Dict], str]:
        """
        统一的检查点状态加载接口
        
        替换原有的空实现，集成权重兼容性功能
        
        Returns:
            tuple: (start_steps, start_epoch, ema_path, model_state_dict, 
                   optimizer_ckpt, lr_scheduler_ckpt, load_state_dict_msg)
        """
        try:
            logger.info("开始加载检查点状态")
            
            # 初始化返回值
            start_steps = 0
            start_epoch = 0
            ema_path = None
            model_state_dict = None
            optimizer_ckpt = None
            lr_scheduler_ckpt = None
            load_state_dict_msg = "未加载任何权重"
            
            # 检查权重路径配置
            if self.model_path and self.processor_path:
                # LORE-TSR格式权重
                logger.info(f"检测到LORE-TSR权重配置:")
                logger.info(f"  模型权重: {self.model_path}")
                logger.info(f"  处理器权重: {self.processor_path}")
                
                model_state_dict = self.load_lore_format_weights(self.model_path, self.processor_path)
                if model_state_dict:
                    load_state_dict_msg = f"成功加载LORE-TSR权重: {len(model_state_dict)} 个参数"
                else:
                    load_state_dict_msg = "LORE-TSR权重加载失败"
                    
            elif self.checkpoint_path:
                # train-anything格式检查点
                logger.info(f"检测到train-anything检查点: {self.checkpoint_path}")
                
                checkpoint_data = self.load_train_anything_weights(self.checkpoint_path)
                if checkpoint_data:
                    model_state_dict = checkpoint_data.get('model_state_dict')
                    optimizer_ckpt = checkpoint_data.get('optimizer_state_dict')
                    lr_scheduler_ckpt = checkpoint_data.get('lr_scheduler_state_dict')
                    start_steps = checkpoint_data.get('step', 0)
                    start_epoch = checkpoint_data.get('epoch', 0)
                    ema_path = checkpoint_data.get('ema_path')
                    
                    load_state_dict_msg = f"成功加载train-anything检查点: step={start_steps}, epoch={start_epoch}"
                else:
                    load_state_dict_msg = "train-anything检查点加载失败"
            
            elif self.load_mode == 'auto':
                # 自动检测模式
                logger.info("自动检测模式，尝试查找权重文件")
                load_state_dict_msg = "自动检测模式：未找到有效的权重文件"
            
            else:
                logger.info("未配置权重路径，使用随机初始化")
                load_state_dict_msg = "未配置权重路径，使用随机初始化"
            
            logger.info(f"检查点状态加载完成: {load_state_dict_msg}")
            
            return (start_steps, start_epoch, ema_path, model_state_dict, 
                   optimizer_ckpt, lr_scheduler_ckpt, load_state_dict_msg)
            
        except Exception as e:
            logger.error(f"加载检查点状态时出错: {e}")
            return (0, 0, None, None, None, None, f"加载失败: {str(e)}")
    
    def detect_weight_format(self, checkpoint_path: str) -> str:
        """
        检测权重文件格式
        
        Args:
            checkpoint_path (str): 权重文件路径
            
        Returns:
            str: 格式类型 ('lore', 'train_anything', 'unknown')
        """
        return self.detector.detect_format(checkpoint_path)
    
    def load_lore_format_weights(self, model_path: str, processor_path: str) -> Optional[Dict]:
        """
        加载LORE-TSR格式权重
        
        Args:
            model_path (str): LORE-TSR模型权重路径
            processor_path (str): LORE-TSR处理器权重路径
            
        Returns:
            dict or None: 加载的权重字典
        """
        try:
            logger.info(f"加载LORE-TSR格式权重")
            
            # 验证文件存在性
            if not validate_weight_file(model_path):
                logger.error(f"模型权重文件无效: {model_path}")
                return None
                
            if not validate_weight_file(processor_path):
                logger.error(f"处理器权重文件无效: {processor_path}")
                return None
            
            # 使用转换器进行实时转换
            state_dict = self.auto_convert_and_load(model_path, processor_path)
            
            if state_dict:
                logger.info(f"LORE-TSR权重加载成功，包含 {len(state_dict)} 个参数")
                return state_dict
            else:
                logger.error("LORE-TSR权重转换失败")
                return None
                
        except Exception as e:
            logger.error(f"加载LORE-TSR格式权重失败: {e}")
            return None
    
    def load_train_anything_weights(self, checkpoint_dir: str) -> Optional[Dict]:
        """
        加载train-anything格式权重
        
        Args:
            checkpoint_dir (str): 检查点目录路径
            
        Returns:
            dict or None: 检查点数据
        """
        try:
            logger.info(f"加载train-anything格式权重: {checkpoint_dir}")
            
            # 检查是否为目录
            if os.path.isdir(checkpoint_dir):
                # 查找pytorch_model.bin文件
                model_file = os.path.join(checkpoint_dir, 'pytorch_model.bin')
                if not os.path.exists(model_file):
                    logger.error(f"未找到模型文件: {model_file}")
                    return None
                    
                # 加载模型权重
                model_state_dict = torch.load(model_file, map_location='cpu')
                
                # 查找其他检查点文件
                checkpoint_data = {
                    'model_state_dict': model_state_dict
                }
                
                # 尝试加载训练状态
                training_state_file = os.path.join(checkpoint_dir, 'training_state.bin')
                if os.path.exists(training_state_file):
                    training_state = torch.load(training_state_file, map_location='cpu')
                    checkpoint_data.update(training_state)
                
                logger.info(f"train-anything权重加载成功")
                return checkpoint_data
                
            else:
                # 单个文件
                if not validate_weight_file(checkpoint_dir):
                    return None
                    
                checkpoint = torch.load(checkpoint_dir, map_location='cpu')
                return {'model_state_dict': checkpoint}
                
        except Exception as e:
            logger.error(f"加载train-anything格式权重失败: {e}")
            return None
    
    def auto_convert_and_load(self, model_path: str, processor_path: str) -> Optional[Dict]:
        """
        自动转换LORE-TSR权重并加载到内存
        
        Args:
            model_path (str): LORE-TSR模型权重路径
            processor_path (str): LORE-TSR处理器权重路径
            
        Returns:
            dict or None: 转换后的模型状态字典
        """
        try:
            logger.info("开始自动转换LORE-TSR权重")
            
            # 创建临时目录
            temp_dir = self._create_temp_checkpoint_dir()
            temp_output = os.path.join(temp_dir, 'converted_pytorch_model.bin')
            
            # 执行转换
            success = self.converter.convert_lore_weights(model_path, processor_path, temp_output)
            
            if success and os.path.exists(temp_output):
                # 加载转换后的权重
                converted_weights = torch.load(temp_output, map_location='cpu')
                
                # 清理临时文件
                try:
                    os.remove(temp_output)
                    os.rmdir(temp_dir)
                except:
                    pass  # 忽略清理错误
                
                logger.info(f"权重自动转换成功，包含 {len(converted_weights)} 个参数")
                return converted_weights
            else:
                logger.error("权重自动转换失败")
                return None
                
        except Exception as e:
            logger.error(f"自动转换权重失败: {e}")
            return None
    
    def validate_loaded_weights(self, state_dict: Dict) -> bool:
        """
        验证加载的权重
        
        Args:
            state_dict (dict): 权重状态字典
            
        Returns:
            bool: 验证是否通过
        """
        try:
            if not isinstance(state_dict, dict):
                logger.error("权重不是字典格式")
                return False
            
            if len(state_dict) == 0:
                logger.error("权重字典为空")
                return False
            
            # 检查权重张量
            for key, value in state_dict.items():
                if not isinstance(value, torch.Tensor):
                    logger.warning(f"权重 {key} 不是张量类型")
                    continue
                
                if torch.isnan(value).any():
                    logger.error(f"权重 {key} 包含NaN值")
                    return False
                
                if torch.isinf(value).any():
                    logger.error(f"权重 {key} 包含无穷值")
                    return False
            
            logger.info(f"权重验证通过，包含 {len(state_dict)} 个参数")
            return True
            
        except Exception as e:
            logger.error(f"验证权重时出错: {e}")
            return False
    
    def _get_weight_paths_from_config(self) -> Tuple[Optional[str], Optional[str]]:
        """从配置中获取权重路径"""
        model_path = self._get_config_value('model.load_model')
        processor_path = self._get_config_value('model.load_processor')
        return model_path, processor_path
    
    def _create_temp_checkpoint_dir(self) -> str:
        """创建临时检查点目录"""
        temp_dir = tempfile.mkdtemp(prefix='lore_tsr_weights_')
        return temp_dir
